import * as path from "path";
import * as fs from "fs";
import * as yaml from "js-yaml";
import * as dotenv from "dotenv";
import { CreateAgentAdapter } from "../../infrastructure/adapters/create-agent.adapter";
import { CreateAgentUseCase } from "../../application/use-cases/create-agent.use-case";
import { CreateTaskAdapter } from "../../infrastructure/adapters/create-task.adapter";
import { CreateTaskUseCase } from "../../application/use-cases/create-task.use-case";
import { CreateWorkflowAdapter } from "../../infrastructure/adapters/create-customer-workflow.adapter";
import { CreateWorkflowUseCase } from "../../application/use-cases/create-customer-workflow.use-case";
import { CreatePortfolioStatusMappingAdapter } from "../../infrastructure/adapters/create-portfolio-item-status-mapping.adapter";
import { CreatePortfolioStatusMappingUseCase } from "../../application/use-cases/create-portfolio-item-status-mapping.use-case";
import { fetch } from "../../common/http-client";

dotenv.config();

const args = process.argv.slice(2);

if (args.length < 2) {
  console.error("\nUsage: npm run workflow:add <workflow-name> <company-name>");
  process.exit(1);
}

const workflowName = args[0].split("=")[1];
const companyName = args[1].split("=")[1];

async function loadYamlFile(filePath: string): Promise<any> {
  if (!fs.existsSync(filePath)) {
    throw new Error("File not found: ${filePath}");
  }
  return yaml.load(fs.readFileSync(filePath, "utf8"));
}

async function fetchCustomerId(companyName: string): Promise<string> {
  const url = `${process.env.API_URL}/api/v1/auth/accounts/nickname/${companyName}`;
  const response = await fetch(companyName, url, "get");
  if (!response.data || !response.data.data || !response.data.data.customerId) {
    throw new Error("Customer ID not found for the provided company nickname.");
  }
  return response.data.data.customerId;
}

async function fetchAgentIdByName(agentName: string): Promise<string | null> {
  const url = `${process.env.API_URL}/api/v1/intelligence/agents/`;
  const response = await fetch(companyName, url, "get");

  if (!response.data || !response.data.data) {
    throw new Error("Failed to fetch agents.");
  }

  const agent = response.data.data.find(
    (agent: any) => agent.name && agent.name.trim() === agentName.trim()
  );

  return agent ? agent : null;
}

async function fetchTaskIdByName(taskName: string): Promise<string | null> {
  const url = `${process.env.API_URL}/api/v1/intelligence/tasks/`;
  const response = await fetch(companyName, url, "get");

  if (!response.data || !response.data.data) {
    throw new Error("Failed to fetch tasks.");
  }

  const task = response.data.data.find(
    (task: any) => task.name && task.name.trim() === taskName.trim()
  );

  return task ? task.id : null;
}

async function createAgent(agentData: any) {
  const adapter = new CreateAgentAdapter();
  const useCase = new CreateAgentUseCase(adapter);

  const existingAgentId = await fetchAgentIdByName(agentData.name);
  if (existingAgentId) {
    return existingAgentId;
  }

  const agent = {
    name: agentData.name,
    role: agentData.role,
    backstory: agentData.backstory,
    llmModel: agentData.llmModel,
    outputType: agentData.outputType || "TEXT",
    lang: agentData.lang || "pt-BR",
    voice: agentData.voice || "QJd9SLe6MVCdF6DR0EAu",
    status: agentData.status || "ACTIVE",
  };

  return await useCase.execute(agent, companyName);
}

async function createTask(taskData: any, agentId: string) {
  const adapter = new CreateTaskAdapter();
  const useCase = new CreateTaskUseCase(adapter);

  // Check if task already exists
  const existingTaskId = await fetchTaskIdByName(taskData.name);
  if (existingTaskId) {
    console.log(`\nTask already exists: ${taskData.name}`);
    return {
      data: { id: existingTaskId, name: taskData.name }
    };
  }

  const task = {
    name: taskData.name,
    description: taskData.description,
    agent: agentId,
    responseTemplate: taskData.responseTemplate || null,
    managerAgentId: taskData.managerAgentId || null,
    status: taskData.status || 'ACTIVE'
  };

  const response = await useCase.execute(task, companyName);
  if (!response || !response.data || !response.data.id) {
    throw new Error(`\nFailed to create task: ${task.name}`);
  }

  console.log(`\nTask created: ${response.data.name}`);
  return response;
}

async function createWorkflow(
  workflowData: any,
  agentId: string,
  tasks: any[]
) {
  const adapter = new CreateWorkflowAdapter();
  const useCase = new CreateWorkflowUseCase(adapter);

  const taskMap = new Map();
  tasks.forEach((task) => {
    taskMap.set(task.originalName, task.data.id);
  });

  // Use workflowData directly, ensuring task IDs are correctly mapped
  workflowData.steps.forEach((step: any) => {
    step.taskId = taskMap.get(step.task);
    step.middlewares?.forEach((middleware: any) => {
      middleware.taskId = taskMap.get(middleware.task);
    });
  });

  const workflowResponse = await useCase.execute(workflowData, companyName);

  if (
    !workflowResponse ||
    !workflowResponse.data ||
    !workflowResponse.data.workflowId
  ) {
    throw new Error("Workflow creation response missing workflowId.");
  }

  return workflowResponse;
}

async function associateWorkflowWithCustomer(
  customerId: string,
  workflowId: string
) {
  const adapter = new CreateWorkflowAdapter();
  await adapter.associateWorkflow(customerId, workflowId, companyName);
}

async function setupStatusMappings(
  customerId: string,
  workflowId: string,
  companyName: string
) {
  const adapter = new CreatePortfolioStatusMappingAdapter();
  const useCase = new CreatePortfolioStatusMappingUseCase(adapter);

  const COMPANY_PATH = path.resolve(
    __dirname,
    `../../data/companies/${companyName}`
  );
  const portfolioStatusMappings = await loadYamlFile(
    path.resolve(COMPANY_PATH, "portfolio-item-status-mapping.yml")
  );

  for (const mapping of portfolioStatusMappings[
    "portfolio-item-status-mapping"
  ]) {
    if (mapping.workflow === workflowName) {
      const statusMapping = {
        workflowId,
        customerId,
        postExecutionResponse: mapping.postExecutionResponse,
        portfolioItemStatus: mapping.portfolioItemStatus,
        responseKey: mapping.responseKey,
      };
      const response = await useCase.execute(statusMapping, companyName);
    }
  }
}

async function processTasksRecursively(
  tasks: any[],
  agentsData: any[],
  createdAgents: any[]
) {
  if (tasks.length === 0) {
    return;
  }

  const [currentTask, ...remainingTasks] = tasks;

  console.log(`\nProcessing task: ${currentTask.name}`);

  let agent = createdAgents.find((a) => a.name === currentTask.agent);

  if (!agent) {
    const agentDetails = agentsData.find((a) => a.name === currentTask.agent);
    if (!agentDetails) {
      console.error(`\nAgent details not found for ${currentTask.agent}`);
      throw new Error(`Agent details not found for ${currentTask.agent}`);
    }

    agent = await createAgent(agentDetails);
    createdAgents.push(agent);
    console.log(`Agent used: ${agentDetails.name}`);
  }

  await processTasksRecursively(remainingTasks, agentsData, createdAgents);
}

async function checkWorkflowExists(
  workflowName: string,
  customerId: string
): Promise<boolean> {
  const url = `${process.env.API_URL}/api/v1/business-base/customers/${customerId}/workflows`;
  const response = await fetch(companyName, url, "get");
  if (!response.data || !response.data.data) {
    throw new Error("Failed to fetch workflows.");
  }

  const existingWorkflow = response.data.data.find(
    (workflow: any) => workflow.name === workflowName
  );

  return !!existingWorkflow;
}

async function addWorkflow(workflowName: string, companyName: string) {
  try {
    console.log(
      `Starting workflow addition for ${workflowName} in company ${companyName}.`
    );

    const customerId = await fetchCustomerId(companyName);

    const workflowExists = await checkWorkflowExists(workflowName, customerId);
    if (workflowExists) {
      console.log(
        `\nWorkflow ${workflowName} already exists for customer ${customerId}. Skipping creation.`
      );
      return;
    }

    const COMPANY_PATH = path.resolve(
      __dirname,
      `../../data/companies/${companyName}`
    );
    const agentsData = await loadYamlFile(
      path.resolve(COMPANY_PATH, "agents.yml")
    );
    const tasksData = await loadYamlFile(
      path.resolve(COMPANY_PATH, "tasks.yml")
    );
    const workflowsData = await loadYamlFile(
      path.resolve(COMPANY_PATH, "workflows.yml")
    );

    const workflowData = workflowsData.workflows.find(
      (workflow: any) => workflow.name === workflowName
    );

    if (!workflowData) {
      throw new Error(`Workflow ${workflowName} not found.`);
    }

    const taskNames = workflowData.steps.flatMap((step: any) => [
      step.task,
      ...(step.middlewares?.map((middleware: any) => middleware.task) || []),
    ]);

    const relevantTasks = tasksData.tasks.filter((task: any) =>
      taskNames.includes(task.name)
    );

    const createdAgents: any[] = [];
    await processTasksRecursively(
      relevantTasks,
      agentsData.agents,
      createdAgents
    );

    const createdTasks = [];
    for (const taskData of relevantTasks) {
      const associatedAgent = createdAgents.find(
        (agent) => agent.name === taskData.agent
      );
      if (associatedAgent) {
        const taskResponse = await createTask(
          taskData,
          associatedAgent.id
        );
        createdTasks.push({
          ...taskResponse,
          originalName: taskData.name,
        });
      }
    }

    const workflowResponse = await createWorkflow(
      workflowData,
      createdAgents[0].id,
      createdTasks
    );

    if (
      !workflowResponse ||
      !workflowResponse.data ||
      !workflowResponse.data.workflowId
    ) {
      throw new Error("Workflow creation response missing workflowId.");
    }

    const workflowId = workflowResponse.data.workflowId;
    console.log(`\nWorkflow created: ${workflowData.name} ID: ${workflowId}`);

    await associateWorkflowWithCustomer(customerId, workflowId);

    await setupStatusMappings(customerId, workflowId, companyName);
    console.log(
      `\nStatus mappings successfully set up for workflow: ${workflowData.name}`
    );

    console.log(`\nWorkflow addition process completed for ${workflowName}.`);
  } catch (error) {
    console.error("\nError adding workflow:", error);
  }
}

addWorkflow(workflowName, companyName).catch((error) => {
  console.error("\nError adding workflow to customer:", error);
  process.exit(1);
});
