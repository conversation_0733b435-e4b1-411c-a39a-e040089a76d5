import * as path from "path";
import * as fs from "fs";
import * as yaml from "js-yaml";
import axios from "axios";
import * as dotenv from "dotenv";
import { v4 as uuidv4 } from "uuid";
import { fetch } from "../../common/http-client";
dotenv.config();

const args = process.argv.slice(2);

if (args.length < 2) {
  console.error("Usage: npm run workflow:edit <workflow-name> <company-name>");
  process.exit(1);
}

const extractArgValue = (arg: string): string | null => {
  const parts = arg.split("=");
  return parts.length === 2 ? parts[1] : null;
};

const workflowName = extractArgValue(args[0]);
const companyName = extractArgValue(args[1]);

if (!workflowName || !companyName) {
  console.error(
    "\nInvalid arguments. Expected format: workflow-name=<value> company-name=<value>"
  );
  process.exit(1);
}

if (!process.env.API_URL) {
  console.error("\nAPI_URL is not set in the environment variables.");
  process.exit(1);
}

async function loadYamlFile(filePath: string): Promise<any> {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }
  const data = yaml.load(fs.readFileSync(filePath, "utf8"));
  console.log("Loading updates in workflows.yml");
  return data;
}

async function fetchCustomerId(companyName: string): Promise<string> {
  const url = `${process.env.API_URL}/api/v1/auth/accounts/nickname/${companyName}`;
  const response = await fetch(companyName, url, "get");
  if (!response.data || !response.data.data || !response.data.data.customerId) {
    throw new Error("Customer ID not found for the provided company nickname.");
  }
  return response.data.data.customerId;
}

async function fetchWorkflowIdByName(
  workflowName: string,
  customerId: string
): Promise<string> {
  if (!companyName) {
    throw new Error("Company name is required but was not provided.");
  }
  const url = `${process.env.API_URL}/api/v1/business-base/customers/${customerId}/workflows/`;
  const response = await fetch(companyName, url, "get");

  if (!response.data || !response.data.data) {
    throw new Error("Failed to fetch workflows.");
  }

  const workflow = response.data.data.find(
    (workflow: any) =>
      workflow.workflowName &&
      workflow.workflowName.trim() === workflowName.trim()
  );

  if (!workflow) {
    throw new Error(`Workflow ${workflowName} not found.`);
  }

  return workflow.workflowId;
}

async function fetchTaskIdByName(taskName: string): Promise<string | null> {
  if (!companyName) {
    throw new Error("Company name is required but was not provided.");
  }
  const url = `${process.env.API_URL}/api/v1/intelligence/tasks/`;
  const response = await fetch(companyName, url, "get");

  if (!response.data || !response.data.data) {
    throw new Error("Failed to fetch tasks.");
  }

  const task = response.data.data.find(
    (task: any) => task.name && task.name.trim() === taskName.trim()
  );

  return task ? task.id : null;
}

async function fetchAgentIdByTaskName(
  taskName: string
): Promise<string | null> {
  if (!companyName) {
    throw new Error("Company name is required but was not provided.");
  }
  const url = `${process.env.API_URL}/api/v1/intelligence/tasks/`;
  const response = await fetch(companyName, url, "get");

  if (!response.data || !response.data.data) {
    throw new Error("Failed to fetch tasks.");
  }

  const task = response.data.data.find(
    (task: any) => task.name && task.name.trim() === taskName.trim()
  );

  return task ? task.agent : null;
}

async function createTaskForMiddleware(
  middleware: any,
  agentId: string
): Promise<string> {
  if (!companyName) {
    throw new Error("Company name is required but was not provided.");
  }
  const taskData = {
    name: middleware.task,
    description: middleware.description,
    agent: agentId,
  };

  const url = `${process.env.API_URL}/api/v1/intelligence/tasks`;
  const response = await fetch(companyName, url, "post", taskData);

  if (
    response.status !== 201 ||
    !response.data ||
    !response.data.data ||
    !response.data.data.id
  ) {
    throw new Error(
      `Failed to create task for middleware: ${response.statusText}`
    );
  }

  console.log(`\nTask created for middleware: ${middleware.name}`);
  return response.data.data.id;
}

async function updateWorkflowFromYaml(
  workflowName: string,
  companyName: string
) {
  try {
    const COMPANY_PATH = path.resolve(
      __dirname,
      `../../data/companies/${companyName}`
    );
    let workflowsData = await loadYamlFile(
      path.resolve(COMPANY_PATH, "workflows.yml")
    );

    let workflow = workflowsData.workflows.find(
      (workflow: any) => workflow.name === workflowName
    );

    if (!workflow) {
      throw new Error(
        `Workflow ${workflowName} not found in YAML for company ${companyName}.`
      );
    }

    const customerId = await fetchCustomerId(companyName);
    const workflowId = await fetchWorkflowIdByName(workflowName, customerId);

    console.log(`\nWorkflow data loaded for:`, workflowName);

    // Primeiro, verifique e crie todas as tasks e middlewares
    for (const step of workflow.steps) {
      let currentTaskId = await fetchTaskIdByName(step.task);
      if (!currentTaskId) {
        const existingAgentId = await fetchAgentIdByTaskName(step.task);
        if (!existingAgentId) {
          throw new Error(`Agent ID not found for task: ${step.task}`);
        }
        currentTaskId = await createTaskForMiddleware(
          { task: step.task, description: step.description },
          existingAgentId
        );
      }

      for (const middleware of step.middlewares) {
        let middlewareTaskId = await fetchTaskIdByName(middleware.task);
        if (!middlewareTaskId) {
          console.log(`\nCreating task for middleware: ${middleware.name}`);
          const existingAgentId = await fetchAgentIdByTaskName(step.task);
          if (!existingAgentId) {
            throw new Error(`\nAgent ID not found for task: ${step.task}`);
          }
          middlewareTaskId = await createTaskForMiddleware(
            middleware,
            existingAgentId
          );
        }
        if (!middlewareTaskId) {
          throw new Error(
            `Failed to create or fetch task ID for middleware: ${middleware.name}`
          );
        }
      }
    }

    const stepsWithIds = await Promise.all(
      workflow.steps.map(async (step: any) => {
        const currentTaskId = await fetchTaskIdByName(step.task);

        const middlewares = await Promise.all(
          step.middlewares.map(async (middleware: any) => {
            const middlewareTaskId = await fetchTaskIdByName(middleware.task);
            return {
              id: middleware.id || uuidv4(),
              name: middleware.name,
              taskId: middlewareTaskId,
              description: middleware.description,
              type: middleware.type,
              showOff: middleware.showOff,
            };
          })
        );

        return {
          stepId: step.stepId || uuidv4(),
          description: step.description,
          taskId: currentTaskId,
          order: step.order || 1,
          middlewares,
        };
      })
    );

    const response = await axios.put(
      `${process.env.API_URL}/api/v1/orchestrator/workflows/${workflowId}`,
      {
        workflowId: workflowId,
        name: workflow.name,
        description: workflow.description,
        status: workflow.status || "ACTIVE",
        steps: stepsWithIds,
      }
    );

    if (response.status !== 200) {
      throw new Error(`Failed to update workflow: ${response.statusText}`);
    }

    console.log(
      `\nWorkflow ${workflowName} updated successfully on the server.`
    );
  } catch (error) {
    console.error("Error updating workflow on server:", error);
    if (axios.isAxiosError(error) && error.response) {
      console.error("Response data:", error.response.data);
    }
    process.exit(1);
  }
}

updateWorkflowFromYaml(workflowName, companyName);
