import axios, { AxiosError } from "axios";
import * as dotenv from "dotenv";
import * as path from "path";
import * as fs from "fs";
import * as yaml from "js-yaml";
dotenv.config();

const args = process.argv.slice(2);

if (args.length < 2) {
  console.error(
    "Usage: npm run workflow:delete workflow-name=<workflow-name> company-name=<company-name>"
  );
  process.exit(1);
}

const extractArgValue = (arg: string): string | null => {
  const parts = arg.split("=");
  return parts.length === 2 ? parts[1] : null;
};

const workflowName = extractArgValue(args[0]);
const companyName = extractArgValue(args[1]);

if (!workflowName || !companyName) {
  console.error(
    "Invalid arguments. Expected format: workflow-name=<value> company-name=<value>"
  );
  process.exit(1);
}

async function loadYamlFile(filePath: string): Promise<any> {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }
  return yaml.load(fs.readFileSync(filePath, "utf8"));
}

async function fetchCustomerId(companyName: string): Promise<string> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/auth/accounts/nickname/${companyName}`
    );

    if (
      !response.data ||
      !response.data.data ||
      !response.data.data.customerId
    ) {
      throw new Error(
        "Customer ID not found for the provided company nickname."
      );
    }

    return response.data.data.customerId;
  } catch (error) {
    console.error("Error fetching customer ID:", error);
    throw new Error("Failed to fetch customer ID.");
  }
}

async function fetchWorkflowIdByName(
  workflowName: string,
  customerId: string
): Promise<string | null> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/business-base/customers/${customerId}/workflows/`
    );

    if (!response.data || !response.data.data) {
      throw new Error("Failed to fetch workflows.");
    }

    const workflow = response.data.data.find((workflow: any) => {
      return (
        workflow.workflowName &&
        workflow.workflowName.trim() === workflowName.trim()
      );
    });

    if (!workflow) {
      console.log(`Workflow ${workflowName} not found.`);
      return null;
    }

    console.log(`Workflow found: ${workflow.workflowName}, ID: ${workflow.id}`);
    return workflow.id;
  } catch (error) {
    console.error("Error fetching workflow ID:", error);
    throw new Error("Failed to fetch workflow ID.");
  }
}

async function fetchTaskIdByName(taskName: string): Promise<string | null> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/intelligence/tasks/`
    );

    if (!response.data || !response.data.data) {
      throw new Error("Failed to fetch tasks.");
    }

    const task = response.data.data.find(
      (task: any) => task.name && task.name.trim() === taskName.trim()
    );

    if (!task) {
      console.log(`Task ${taskName} not found.`);
      return null;
    }

    return task.id;
  } catch (error) {
    console.error("Error fetching task ID:", error);
    throw new Error("Failed to fetch task ID.");
  }
}

async function fetchTasksByWorkflowId(workflowId: string): Promise<any[]> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/orchestrator/workflows/${workflowId}/tasks`
    );

    if (!response.data || !response.data.data) {
      console.log("No tasks found for this workflow.");
      return [];
    }

    return response.data.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response && error.response.status === 404) {
        console.log("Tasks not found for this workflow, skipping...");
        return [];
      }
    }
    console.error("Error fetching tasks:", error);
    throw new Error("Failed to fetch tasks.");
  }
}

async function deleteTaskById(taskId: string): Promise<void> {
  try {
    const response = await axios.delete(
      `${process.env.API_URL}/api/v1/intelligence/tasks/${taskId}`
    );

    if (response.status === 200) {
      console.log(`Task with ID ${taskId} deleted successfully.`);
    } else {
      console.error(
        `Failed to delete task with ID ${taskId}. Status: ${response.status}`
      );
    }
  } catch (error) {
    console.error("Error deleting task:", error);
    throw new Error("Failed to delete task.");
  }
}

async function fetchAgentIdByName(agentName: string): Promise<string | null> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/intelligence/agents/`
    );

    if (!response.data || !response.data.data) {
      throw new Error("Failed to fetch agents.");
    }

    const agent = response.data.data.find(
      (agent: any) => agent.name && agent.name.trim() === agentName.trim()
    );

    if (!agent) {
      console.log(`Agent ${agentName} not found.`);
      return null;
    }

    return agent.id;
  } catch (error) {
    console.error("Error fetching agent ID:", error);
    throw new Error("Failed to fetch agent ID.");
  }
}

async function fetchAgentsByWorkflowId(workflowId: string): Promise<any[]> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/orchestrator/workflows/${workflowId}/agents`
    );

    if (!response.data || !response.data.data) {
      console.log("No agents found for this workflow.");
      return [];
    }

    return response.data.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response && error.response.status === 404) {
        console.log("Agents not found for this workflow, skipping...");
        return [];
      }
    }
    console.error("Error fetching agents:", error);
    throw new Error("Failed to fetch agents.");
  }
}

async function deleteAgentById(agentId: string): Promise<void> {
  try {
    const response = await axios.delete(
      `${process.env.API_URL}/api/v1/intelligence/agents/${agentId}`
    );

    if (response.status === 200) {
      console.log(`Agent with ID ${agentId} deleted successfully.`);
    } else {
      console.error(
        `Failed to delete agent with ID ${agentId}. Status: ${response.status}`
      );
    }
  } catch (error) {
    console.error("Error deleting agent:", error);
    throw new Error("Failed to delete agent.");
  }
}

async function deleteTasksAndAgents(workflowName: string, companyName: string) {
  const COMPANY_PATH = path.resolve(
    __dirname,
    `../data/companies/${companyName}`
  );

  const workflowsData = await loadYamlFile(
    path.resolve(COMPANY_PATH, "workflows.yml")
  );

  const tasksData = await loadYamlFile(path.resolve(COMPANY_PATH, "tasks.yml"));

  const workflow = workflowsData.workflows.find(
    (workflow: any) => workflow.name === workflowName
  );

  if (!workflow) {
    throw new Error(`Workflow ${workflowName} not found in YAML.`);
  }

  const taskNames = workflow.steps.flatMap((step: any) => [
    step.task,
    ...(step.middlewares?.map((middleware: any) => middleware.task) || []),
  ]);

  const agentNames = new Set<string>(
    taskNames
      .map((taskName: string) => {
        const task = tasksData.tasks.find(
          (task: any) => task.name === taskName
        );
        return task ? task.agent : null;
      })
      .filter(
        (agentName: string | null): agentName is string => agentName !== null
      )
  );

  for (const taskName of taskNames) {
    const taskId = await fetchTaskIdByName(taskName);
    if (taskId) {
      await deleteTaskById(taskId);
    }
  }

  for (const agentName of agentNames) {
    if (agentName) {
      const agentId = await fetchAgentIdByName(agentName);
      if (agentId) {
        await deleteAgentById(agentId);
      }
    }
  }
}

// async function deleteWorkflowById(id: string): Promise<void> {
//   try {
//     const response = await axios.delete(
//       `${process.env.API_URL}/api/v1/orchestrator/workflows/${id}`
//    );

//     if (response.status === 200) {
//       console.log(`Workflow com ID ${id} apagado com sucesso.`);
//     } else {
//       console.error(
//         `Falha ao apagar o workflow com ID ${id}. Status: ${response.status}`
//       );
//     }
//   } catch (error) {
//     console.error("Erro ao apagar o workflow:", error);
//     throw new Error("Falha ao apagar o workflow.");
//   }
// }

async function deleteWorkflowWithDependencies(
  workflowId: string,
  companyName: string
) {
  try {
    if (workflowName) {
      // Delete tasks and agents first
      await deleteTasksAndAgents(workflowName, companyName);
    } else {
      throw new Error("Workflow name is null.");
    }

    // Now delete the workflow
    // await deleteWorkflowById(workflowId);
    // console.log(
    //   `Workflow with ID ${workflowId} and its dependencies deleted successfully.`
    // );
  } catch (error) {
    console.error("Error deleting workflow and its dependencies:", error);
  }
}

(async () => {
  try {
    const customerId = await fetchCustomerId(companyName);
    const workflowId = await fetchWorkflowIdByName(workflowName, customerId);
    if (!workflowId) {
      console.error(`Workflow with name ${workflowName} not found.`);
      process.exit(1);
    }
    await deleteWorkflowWithDependencies(workflowId, companyName);
  } catch (error) {
    console.error("Error in the deletion process:", error);
    process.exit(1);
  }
})();
