import * as path from "path";
import * as fs from "fs";
import * as yaml from "js-yaml";
import { fetch } from "../../common/http-client";
import * as dotenv from "dotenv";

dotenv.config();

const args = process.argv.slice(2);

if (args.length < 2) {
  console.error(
    "Usage: npm run portfolio:edit <portfolio-name> <company-name>"
  );
  process.exit(1);
}

const extractArgValue = (arg: string): string | null => {
  const parts = arg.split("=");
  return parts.length === 2 ? parts[1] : null;
};

const portfolioName = extractArgValue(args[0]);
const companyName = extractArgValue(args[1]);

if (!portfolioName || !companyName) {
  console.error(
    "\nInvalid arguments. Expected format: portfolio-name=<value> company-name=<value>"
  );
  process.exit(1);
}

if (!process.env.API_URL) {
  console.error("\nAPI_URL is not set in the environment variables.");
  process.exit(1);
}

async function loadYamlFile(filePath: string): Promise<any> {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }
  const data = yaml.load(fs.readFileSync(filePath, "utf8"));
  return data;
}

async function fetchPortfolioIdByName(
  portfolioName: string,
  companyName: string
): Promise<string> {
  try {
    const url = `${process.env.API_URL}/api/v1/business-base/portfolios/`;
    const response = await fetch(companyName, url, "get");

    if (!response.data || !response.data.data) {
      throw new Error("Failed to fetch portfolios.");
    }

    const portfolio = response.data.data.find(
      (portfolio: any) =>
        portfolio.name && portfolio.name.trim() === portfolioName.trim()
    );

    if (!portfolio) {
      throw new Error(`Portfolio ${portfolioName} not found.`);
    }

    return portfolio.id;
  } catch (error) {
    console.error("Error fetching portfolio ID:", error);
    throw new Error("Failed to fetch portfolio ID.");
  }
}

async function updatePortfolioFromYaml(
  portfolioName: string,
  companyName: string
) {
  try {
    const COMPANY_PATH = path.resolve(
      __dirname,
      `../../data/companies/${companyName}`
    );
    const portfoliosData = await loadYamlFile(
      path.resolve(COMPANY_PATH, "portfolios.yml")
    );

    const portfolio = portfoliosData.portfolios.find(
      (portfolio: any) => portfolio.name === portfolioName
    );

    if (!portfolio) {
      throw new Error(
        `Portfolio ${portfolioName} not found in YAML for company ${companyName}.`
      );
    }

    const portfolioId = await fetchPortfolioIdByName(
      portfolioName,
      companyName
    );

    console.log(`Updating portfolio: ${portfolioName}`);

    const url = `${process.env.API_URL}/api/v1/business-base/portfolios/${portfolioId}`;
    const response = await fetch(companyName, url, "put", portfolio);

    if (response.status !== 200) {
      throw new Error(`Failed to update portfolio: ${response.statusText}`);
    }

    console.log(
      `\nPortfolio ${portfolioName} updated successfully on the server.`
    );
  } catch (error) {
    console.error("Error updating portfolio on server:", error);
    process.exit(1);
  }
}

updatePortfolioFromYaml(portfolioName, companyName);
