import * as path from "path";
import * as fs from "fs";
import * as yaml from "js-yaml";
import { CreatePortfolioRequestDto } from "../../infrastructure/dtos/in/create-portfolio-request.dto";
import * as dotenv from "dotenv";
import { fetch } from "../../common/http-client";
import { CreatePortfolioResponseDto } from "../../infrastructure/dtos/out/create-portfolio-response.dto";
dotenv.config();

const args = process.argv.slice(2);

if (args.length < 2) {
  console.error(
    "\nUsage: npm run portfolio:add -- --name=<portfolio-name> --company-name=<company-name>"
  );
  process.exit(1);
}

const extractArgValue = (arg: string): string | null => {
  const parts = arg.split("=");
  return parts.length === 2 ? parts[1] : null;
};

const portfolioName = extractArgValue(args[0]);
const companyName = extractArgValue(args[1])!;

if (!portfolioName || !companyName) {
  console.error(
    "\nInvalid arguments. Expected format: --name=<portfolio-name> --company-name=<company-name>"
  );
  process.exit(1);
}

if (!companyName) {
  throw new Error("Company name is required but was not provided.");
}

async function loadYamlFile(filePath: string): Promise<any> {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }
  return yaml.load(fs.readFileSync(filePath, "utf8"));
}

async function fetchCustomerId(companyName: string): Promise<string> {
  try {
    const response = await fetch(
      companyName,
      `${process.env.API_URL}/api/v1/auth/accounts/nickname/${companyName}`,
      "get"
    );

    if (
      !response.data ||
      !response.data.data ||
      !response.data.data.customerId
    ) {
      throw new Error(
        "Customer ID not found for the provided company nickname."
      );
    }

    return response.data.data.customerId;
  } catch (error) {
    console.error("\nError fetching customer ID:", error);
    throw new Error("Failed to fetch customer ID.");
  }
}

async function fetchWorkflowId(
  workflowName: string,
  customerId: string
): Promise<string> {
  try {
    const response = await fetch(
      companyName,
      `${process.env.API_URL}/api/v1/business-base/customers/${customerId}/workflows/`,
      "get"
    );

    if (!response.data || !response.data.data) {
      throw new Error("Failed to fetch workflows.");
    }

    const workflow = response.data.data.find(
      (workflow: any) => workflow.workflowName === workflowName
    );

    if (!workflow) {
      throw new Error(`Workflow ${workflowName} not found.`);
    }

    return workflow.workflowId;
  } catch (error) {
    console.error("\nError fetching workflow ID:", error);
    throw new Error("Failed to fetch workflow ID.");
  }
}

async function fetchExistingPortfolios(customerId: string): Promise<string[]> {
  const url = `${process.env.API_URL}/api/v1/business-base/portfolios/`;
  const response = await fetch(companyName, url, "get");
  if (!response.data || !response.data.data) {
    throw new Error("Failed to fetch existing portfolios.");
  }
  return response.data.data.map((portfolio: any) => portfolio.name);
}

async function createPortfolioFromYaml(
  companyName: string,
  portfolioName: string
) {
  const COMPANY_PATH = path.resolve(
    __dirname,
    `../../data/companies/${companyName}`
  );

  const portfolioFilePath = path.resolve(COMPANY_PATH, "portfolios.yml");

  const portfolioData = await loadYamlFile(portfolioFilePath);

  const portfolio = portfolioData.portfolios.find(
    (p: any) => p.name === portfolioName
  );
  if (!portfolio) {
    throw new Error(`Portfolio ${portfolioName} not found in YAML.`);
  }

  const customerId = await fetchCustomerId(companyName);

  const existingPortfolios = await fetchExistingPortfolios(customerId);

  if (existingPortfolios.includes(portfolioName)) {
    console.log(
      `Portfolio ${portfolioName} already exists. Skipping creation.`
    );
    return;
  }

  const workflowId = await fetchWorkflowId(portfolio.workflow, customerId);

  // Adiciona o workflowId ao objeto do YAML
  portfolio.workflowId = workflowId;

  try {
    console.log(`Creating portfolio for workflow: ${portfolio.workflow}`);
    const url = `${process.env.API_URL}/api/v1/business-base/portfolios/`;
    const response = await fetch(companyName, url, "post", {
      ...portfolio,
      customerId,
    });
    console.log(
      `\nPortfolio created successfully for workflow: ${portfolio.workflow}`
    );
  } catch (error) {
    console.error(`Error creating portfolio ${portfolio.name}:`, error);
  }
}

createPortfolioFromYaml(companyName, portfolioName).catch((error) => {
  console.error("\nError creating portfolio:", error);
  process.exit(1);
});

export { createPortfolioFromYaml };
