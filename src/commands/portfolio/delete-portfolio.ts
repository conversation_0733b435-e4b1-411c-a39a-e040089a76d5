import * as path from "path";
import axios from "axios";
import * as dotenv from "dotenv";
import * as fs from "fs";
import * as yaml from "js-yaml";
import { fetch } from "../../common/http-client";
dotenv.config();

const args = process.argv.slice(2);

if (args.length < 2) {
  console.error(
    "\nUsage: npm run portfolio:delete portfolio-name=<portfolio-name> company-name=<company-name>"
  );
  process.exit(1);
}

const portfolioName = args[0].split("=")[1];
const companyName = args[1].split("=")[1];

if (!portfolioName || !companyName) {
  console.error(
    "\nPlease provide a portfolio name and company name in the format: portfolio-name=<portfolio-name> company-name=<company-name>"
  );
  process.exit(1);
}

async function fetchCustomerId(companyName: string): Promise<string> {
  const response = await axios.get(
    `${process.env.API_URL}/api/v1/auth/accounts/nickname/${companyName}`
  );
  if (!response.data || !response.data.data || !response.data.data.customerId) {
    throw new Error("Customer ID not found for the given company nickname.");
  }
  return response.data.data.customerId;
}

async function fetchPortfolioIdByName(
  portfolioName: string,
  customerId: string,
  companyName: string
): Promise<string> {
  const url = `${process.env.API_URL}/api/v1/business-base/portfolios/`;
  const response = await fetch(companyName, url, "get");

  if (!response.data || !response.data.data) {
    throw new Error("Failed to fetch portfolios.");
  }

  const portfolio = response.data.data.find(
    (portfolio: any) =>
      portfolio.name && portfolio.name.trim() === portfolioName.trim()
  );

  if (!portfolio) {
    throw new Error(`Portfolio ${portfolioName} not found.`);
  }

  return portfolio.id;
}

async function loadYamlFile(filePath: string): Promise<any> {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }
  return yaml.load(fs.readFileSync(filePath, "utf8"));
}

async function fetchPortfolioNameFromYaml(
  companyName: string,
  portfolioName: string
): Promise<string> {
  const COMPANY_PATH = path.resolve(
    __dirname,
    `../../data/companies/${companyName}`
  );
  const portfoliosData = await loadYamlFile(
    path.resolve(COMPANY_PATH, "portfolios.yml")
  );

  const portfolio = portfoliosData.portfolios.find(
    (p: any) => p.name === portfolioName
  );
  if (!portfolio) {
    throw new Error(`Portfolio ${portfolioName} not found in YAML.`);
  }

  return portfolio.name;
}

async function deletePortfolio(portfolioName: string, companyName: string) {
  try {
    const customerId = await fetchCustomerId(companyName);
    const portfolioNameFromYaml = await fetchPortfolioNameFromYaml(
      companyName,
      portfolioName
    );
    const portfolioId = await fetchPortfolioIdByName(
      portfolioNameFromYaml,
      customerId,
      companyName
    );
    const url = `${process.env.API_URL}/api/v1/business-base/portfolios/${portfolioId}`;
    const response = await fetch(companyName, url, "delete");

    if (response.status === 200) {
      console.log(`Portfolio ${portfolioName} deleted successfully.`);
    } else {
      console.error(`Failed to delete portfolio ${portfolioName}.`);
    }
  } catch (error) {
    console.error(`Error deleting portfolio ${portfolioName}:`, error);
  }
}

deletePortfolio(portfolioName, companyName).catch((error) => {
  console.error("\nError in deleting portfolio:", error);
  process.exit(1)
});