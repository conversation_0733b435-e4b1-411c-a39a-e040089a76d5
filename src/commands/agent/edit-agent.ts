import * as path from "path";
import * as fs from "fs";
import * as yaml from "js-yaml";
import axios from "axios";
import * as dotenv from "dotenv";
import { fetch } from "../../common/http-client";
dotenv.config();

const args = process.argv.slice(2);

if (args.length < 2) {
  console.error("Usage: npm run agent:edit <agent-name> <company-name>");
  process.exit(1);
}

const extractArgValue = (arg: string): string | null => {
  const parts = arg.split("=");
  return parts.length === 2 ? parts[1] : null;
};

const agentName = extractArgValue(args[0]);
const companyName = extractArgValue(args[1]);

if (!agentName || !companyName) {
  console.error(
    "\nInvalid arguments. Expected format: agent-name=<value> company-name=<value>"
  );
  process.exit(1);
}

if (!process.env.API_URL) {
  console.error("\nAPI_URL is not set in the environment variables.");
  process.exit(1);
}

async function loadYamlFile(filePath: string): Promise<any> {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }
  const data = yaml.load(fs.readFileSync(filePath, "utf8"));
  return data;
}

async function fetchCustomerId(companyName: string): Promise<string> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/auth/accounts/nickname/${companyName}`
    );

    return response.data?.data?.customerId || null;
  } catch (error) {
    console.error("Error fetching customer ID:", error);
    throw new Error("Failed to fetch customer ID.");
  }
}

async function fetchAgentIdByName(agentName: string): Promise<string> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/intelligence/agents/`
    );

    if (!response.data || !response.data.data) {
      throw new Error("Failed to fetch agents.");
    }

    // Trim and compare names to avoid issues with spaces
    const agent = response.data.data.find(
      (agent: any) => agent.name && agent.name.trim() === agentName.trim()
    );

    if (!agent) {
      throw new Error(`Agent ${agentName} not found.`);
    }

    return agent.id;
  } catch (error) {
    console.error("Error fetching agent ID:", error);
    throw new Error("Failed to fetch agent ID.");
  }
}

async function updateAgentDescriptionFromYaml(
  agentName: string,
  companyName: string
) {
  try {
    const COMPANY_PATH = path.resolve(
      __dirname,
      `../../data/companies/${companyName}`
    );
    const agentsData = await loadYamlFile(
      path.resolve(COMPANY_PATH, "agents.yml")
    );

    const agent = agentsData.agents.find(
      (agent: any) => agent.name === agentName
    );

    if (!agent) {
      throw new Error(
        `Agent ${agentName} not found in YAML for company ${companyName}.`
      );
    }

    const agentId = await fetchAgentIdByName(agentName);

    const response = await fetch(
      companyName,
      `${process.env.API_URL}/api/v1/intelligence/agents/${agentId}`,
      "put",
      agent
    );

    if (response.status !== 200) {
      throw new Error(
        `Failed to update agent description: ${response.statusText}`
      );
    }

    console.log(`Agent ${agentName} updated successfully on the server.`);
  } catch (error) {
    console.error("Error updating agent description on server:", error);
    process.exit(1);
  }
}

updateAgentDescriptionFromYaml(agentName, companyName);
