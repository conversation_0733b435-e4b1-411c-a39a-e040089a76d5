import axios from "axios";
import * as dotenv from "dotenv";
import { fetch } from "../../common/http-client";
dotenv.config();

const args = process.argv.slice(2);

if (args.length < 2) {
  console.error(
    "\nUsage: npm run agent:delete agent-name=<agent-name> company-name=<company-name>"
  );
  process.exit(1);
}

const extractArgValue = (arg: string): string | null => {
  const parts = arg.split("=");
  return parts.length === 2 ? parts[1] : null;
};

const agentName = extractArgValue(args[0]);
const companyName = extractArgValue(args[1]);

if (!agentName || !companyName) {
  console.error(
    "\nInvalid arguments. Expected format: agent-name=<value> company-name=<value>"
  );
  process.exit(1);
}

async function fetchAgentIdByName(agentName: string): Promise<string | null> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/intelligence/agents/`
    );

    if (!response.data || !response.data.data) {
      throw new Error("Failed to fetch agents.");
    }

    const agent = response.data.data.find(
      (agent: any) => agent.name && agent.name.trim() === agentName.trim()
    );

    if (!agent) {
      console.log(`Agent ${agentName} not found.`);
      return null;
    }

    return agent.id;
  } catch (error) {
    console.error("\nError fetching agent ID:", error);
    throw new Error("Failed to fetch agent ID.");
  }
}

async function deleteAgent(agentId: string, companyName: string) {
  try {
    const url = `${process.env.API_URL}/api/v1/intelligence/agents/${agentId}`;
    const response = await fetch(companyName, url, "delete");

    if (response.status === 200) {
      console.log(`Agent ${agentName} deleted successfully.`);
    } else {
      console.error(
        `\nFailed to delete agent with ID ${agentId}. Status: ${response.status}`
      );
    }
  } catch (error) {
    console.error("\nError deleting agent:", error);
    throw new Error("Failed to delete agent.");
  }
}

(async () => {
  try {
    const agentId = await fetchAgentIdByName(agentName);
    if (!agentId) {
      console.error(`\nAgent with name ${agentName} not found.`);
      process.exit(1);
    }
    await deleteAgent(agentId, companyName);
  } catch (error) {
    console.error("\nError in deletion process:", error);
    process.exit(1);
  }
})();
