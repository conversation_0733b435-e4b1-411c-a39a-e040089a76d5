import * as dotenv from "dotenv";
dotenv.config();
import * as path from "path";
import * as fs from "fs";
import * as yaml from "js-yaml";
import { v4 as uuidv4 } from "uuid";
import { AccountSignupAdapter } from "../../infrastructure/adapters/account-signup.adapter";
import { AccountSignupUseCase } from "../../application/use-cases/account-signup.use-case";
import { CreateAgentAdapter } from "../../infrastructure/adapters/create-agent.adapter";
import { CreateAgentUseCase } from "../../application/use-cases/create-agent.use-case";
import { CreateTaskAdapter } from "../../infrastructure/adapters/create-task.adapter";
import { CreateTaskUseCase } from "../../application/use-cases/create-task.use-case";
import { CreateWorkflowAdapter } from "../../infrastructure/adapters/create-customer-workflow.adapter";
import { CreateWorkflowUseCase } from "../../application/use-cases/create-customer-workflow.use-case";
import { CreatePortfolioStatusMappingAdapter } from "../../infrastructure/adapters/create-portfolio-item-status-mapping.adapter";
import { CreatePortfolioStatusMappingUseCase } from "../../application/use-cases/create-portfolio-item-status-mapping.use-case";
import { LoginAdapter } from "../../infrastructure/adapters/login.adapter";
import { LoginRequestDto } from "../../infrastructure/dtos/in/login-request.dto";

interface SetupResources {
  agents: any[];
  tasks: any[];
  workflow: any;
  company: any;
}

class SetupScript {
  private readonly COMPANY_PATH: string;

  constructor(private companyName: string) {
    this.COMPANY_PATH = path.resolve(
      __dirname,
      `../../data/companies/${companyName}`
    );
  }

  public async run() {
    try {
      // 1. Validate required files
      this.validateSetup();

      // 2. Load YAML data
      const data = await this.loadData();

      // 3. Create resources in API
      const resources = await this.createResources(data);

      // 4. Setup integrations
      await this.setupIntegrations(resources);

      // 5. Finish setup with login test
      await this.finishSetup(resources);
    } catch (error) {
      console.error("Error during setup:", JSON.stringify(error));
      process.exit(1);
    }
  }

  private validateSetup() {
    if (!this.companyName) {
      console.error("Usage: npm run company -- <companyname>");
      process.exit(1);
    }

    const requiredFiles = [
      "agents.yml",
      "tasks.yml",
      "workflows.yml",
      "company.yml",
    ];

    for (const file of requiredFiles) {
      const filePath = path.resolve(this.COMPANY_PATH, file);
      if (!fs.existsSync(filePath)) {
        console.error(`Required file not found: ${filePath}`);
        process.exit(1);
      }
    }
  }

  private loadYamlFile(filePath: string): any {
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }
    return yaml.load(fs.readFileSync(filePath, "utf8"));
  }

  private async loadData() {
    const agentsData = this.loadYamlFile(
      path.resolve(this.COMPANY_PATH, "agents.yml")
    );
    const tasksData = this.loadYamlFile(
      path.resolve(this.COMPANY_PATH, "tasks.yml")
    );
    const workflowData = this.loadYamlFile(
      path.resolve(this.COMPANY_PATH, "workflows.yml")
    );
    const companyData = this.loadYamlFile(
      path.resolve(this.COMPANY_PATH, "company.yml")
    );

    const accountData = {
      nickname: this.companyName,
    };

    return {
      agents: agentsData.agents,
      tasks: tasksData.tasks,
      workflows: workflowData.workflows,
      company: {
        ...companyData,
        accountData,
      },
    };
  }

  private async createResources(data: any): Promise<SetupResources> {
    const createdAgents = [];
    const createdTasks = [];

    // 1. Create Company/Customer first
    data = {
      ...data,
      accountData: {
        nickname: this.companyName,
      },
    };

    const companyResponse = await this.createCompany(data.company);

    // 2. Create Agents
    for (const agentData of data.agents) {
      const agentResponse = await this.createAgent(agentData);
      console.log("\nAgent created:", agentResponse.data.name);
      createdAgents.push(agentResponse);
    }

    // 3. Create Tasks and link to agents
    for (const taskData of data.tasks) {
      const associatedAgent = createdAgents.find(
        (agent) => agent.data.name === taskData.agent
      );
      if (!associatedAgent) {
        throw new Error(`Agent ${taskData.agent} not found in created agents`);
      }
      const taskResponse = await this.createTask(
        taskData,
        associatedAgent.data.id
      );
      console.log("\nTask created:", taskResponse.data.name);
      createdTasks.push(taskResponse);
    }

    // 4. Create Workflow with steps and middlewares
    const workflowResponse = await this.createWorkflow(data, {
      agentId: createdAgents[0].data.id,
      tasks: createdTasks,
    });

    return {
      company: companyResponse,
      agents: createdAgents,
      tasks: createdTasks,
      workflow: workflowResponse,
    };
  }

  private async createAgent(agentData: any) {
    const adapter = new CreateAgentAdapter();
    const useCase = new CreateAgentUseCase(adapter);

    return await useCase.execute(agentData, this.companyName);
  }

  private async createTask(taskData: any, agentId: string) {
    const adapter = new CreateTaskAdapter();
    const useCase = new CreateTaskUseCase(adapter);

    taskData.agent = agentId;

    return await useCase.execute(taskData, this.companyName);
  }

  private async createWorkflow(
    data: any,
    { agentId, tasks }: { agentId: string; tasks: any[] }
  ) {
    // Map tasks using original name
    const taskMap = new Map();
    tasks.forEach((task) => {
      taskMap.set(task.data.name, task.data.id);
    });

    const workflows = data.workflows.map((workflow: any) => {
      const steps = workflow.steps.map((step: any) => {
        step.id = uuidv4();
        const taskId = taskMap.get(step.task);
        if (!taskId) {
          throw new Error(
            `Task ${
              step.task
            } not found in created tasks. Available tasks: ${Array.from(
              taskMap.keys()
            ).join(", ")}`
          );
        }

        step.taskId = taskId;

        step.middlewares = step.middlewares
          ? step.middlewares?.map((middleware: any) => {
              middleware.id = uuidv4();
              if (middleware.task) {
                const middlewareTaskId = taskMap.get(middleware.task);
                if (!middlewareTaskId) {
                  throw new Error(
                    `Middleware task ${
                      middleware.task
                    } not found in created tasks. Available tasks: ${Array.from(
                      taskMap.keys()
                    ).join(", ")}`
                  );
                }
                middleware.taskId = middlewareTaskId;
              }
              return middleware;
            })
          : [];

        return step;
      });

      return {
        ...workflow,
        steps,
      };
    });

    const adapter = new CreateWorkflowAdapter();
    const useCase = new CreateWorkflowUseCase(adapter);
    const workflowResponses = await Promise.all(
      workflows.map((workflow: any) =>
        useCase.execute(workflow, this.companyName)
      )
    );

    return workflowResponses.map((response: any, index: number) => {
      if (!response?.data?.workflowId) {
        throw new Error("Workflow creation response missing workflowId.");
      }
      console.log(
        "\nWorkflow created:",
        workflows[index].name,
        "ID:",
        response.data.workflowId
      );
      return {
        workflowId: response.data.workflowId,
        name: workflows[index].name,
      };
    });
  }

  private async createCompany(companyData: any) {
    const adapter = new AccountSignupAdapter();
    const useCase = new AccountSignupUseCase(adapter);

    return await useCase.execute(companyData);
  }

  private async setupIntegrations(resources: SetupResources) {
    try {
      // 1. Create status mappings
      await this.setupStatusMappings(resources);

      // 2. Associate workflow with customer
      await this.associateWorkflowWithCustomer(resources);

      console.log(
        `\nCustomer ID for ${this.companyName}:`,
        resources.company.data.customer.id
      );

      console.log("\nSetup completed successfully");
    } catch (error) {
      console.error("Error setting up integrations:", error);
      throw error;
    }
  }

  private async setupStatusMappings(resources: SetupResources) {
    const adapter = new CreatePortfolioStatusMappingAdapter();
    const useCase = new CreatePortfolioStatusMappingUseCase(adapter);

    const portfolioStatusMappings = this.loadPortfolioStatusMappings();

    for (const mapping of portfolioStatusMappings[
      "portfolio-item-status-mapping"
    ]) {
      const workflow = resources.workflow.find(
        (wf: any) => wf.name === mapping.workflow
      );
      if (!workflow) {
        throw new Error(
          `Workflow ID not found for mapping: ${mapping.workflow}`
        );
      }

      const statusMapping = {
        ...mapping,
        workflowId: workflow.workflowId,
        customerId: resources.company.data.customer.id,
      };

      const response = await useCase.execute(statusMapping, this.companyName);
      console.log(
        `\nStatus Mapping response for workflow: '${mapping.workflow}'`
      );
    }
  }

  private async associateWorkflowWithCustomer(resources: SetupResources) {
    const adapter = new CreateWorkflowAdapter();
    for (const workflow of resources.workflow) {
      await adapter.associateWorkflow(
        resources.company.data.customer.id,
        workflow.workflowId,
        this.companyName
      );
      console.log(
        `Workflow ${workflow.name} successfully associated with ${this.companyName}.`
      );
    }
  }

  private async finishSetup(resources: SetupResources) {
    try {
      // Test login with created user
      const loginData: LoginRequestDto = {
        email: resources.company.data.user.email,
        password: this.loadYamlFile(
          path.resolve(this.COMPANY_PATH, "company.yml")
        ).userData.password,
      };

      const loginAdapter = new LoginAdapter();
      const loginResponse = await loginAdapter.login(
        loginData,
        this.companyName
      );

      if (loginResponse.data?.token) {
        console.log("\nLogin successful! Token received.");
      }
    } catch (error) {
      console.error("Error during login:", error);
      throw error;
    }
  }

  private loadPortfolioStatusMappings(): any {
    const filePath = path.resolve(
      this.COMPANY_PATH,
      "portfolio-item-status-mapping.yml"
    );
    return this.loadYamlFile(filePath);
  }
}

const companyName = process.argv[2];
const setupScript = new SetupScript(companyName);
setupScript.run().catch((error) => {
  console.error("Failed to run setup:", error);
  process.exit(1);
});
