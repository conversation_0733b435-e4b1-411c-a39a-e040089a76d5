import "reflect-metadata";
import * as path from "path";
import * as fs from "fs";
import * as yaml from "js-yaml";
import * as dotenv from "dotenv";
import axios from "axios";
import { UpdateCustomerPreferencesAdapter } from "../../infrastructure/adapters/update-customer-preferences.adapter";
import { UpdateCustomerPreferencesUseCase } from "../../application/use-cases/update-customer-preferences.use-case";
import { CreateCustomerPreferencesRequestDto, PortfolioPreferencesRequestDto } from "../../infrastructure/dtos/in/upsert-customer-preferences-request.dto";

dotenv.config();

// Argument verification
const args = process.argv.slice(2);

if (args.length < 1) {
  console.error("\nUsage: npm run customer-preferences:update company=<company-name>");
  process.exit(1);
}

const companyName = args[0].split("=")[1];
if (!companyName) {
  console.error(
    "\nPlease provide a company name in the format npm run customer-preferences:update company=<company-name>"
  );
  process.exit(1);
}

function loadYamlFile(filePath: string): any {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }
  return yaml.load(fs.readFileSync(filePath, "utf8"));
}

async function fetchCustomerId(companyName: string): Promise<string> {
  const response = await axios.get(
    `${process.env.API_URL}/api/v1/auth/accounts/nickname/${companyName}`
  );
  if (!response.data || !response.data.data || !response.data.data.customerId) {
    throw new Error("Customer ID not found for the given company nickname.");
  }
  return response.data.data.customerId;
}

async function updateCustomerPreferences(companyName: string) {
  const COMPANY_PATH = path.resolve(
    __dirname,
    `../../data/companies/${companyName}`
  );

  const preferencesFilePath = path.resolve(COMPANY_PATH, "customer-preferences.yml");

  if (!fs.existsSync(preferencesFilePath)) {
    throw new Error(`Customer preferences update file not found: ${preferencesFilePath}`);
  }

  const preferencesData = loadYamlFile(preferencesFilePath);

  const customerId = await fetchCustomerId(companyName);

  // Create portfolio preferences DTO
  const portfolioPreferences = new PortfolioPreferencesRequestDto(
    preferencesData.portfolio.defaultWorkflowId,
    preferencesData.portfolio.timezoneUTC,
    preferencesData.portfolio.importCronExpression,
    preferencesData.portfolio.followUpWorkflowId,
    preferencesData.portfolio.followUpCronExpression,
    preferencesData.portfolio.followUpQuantity,
    preferencesData.portfolio.followUpIntervalMinutes,
    preferencesData.portfolio.exportColumns,
    preferencesData.portfolio.customImportConfig,
    preferencesData.portfolio.statsConfig,
    preferencesData.portfolio.exportConfig
  );


  // Build preferences object dynamically
  const additionalPreferences: any = {};

  // Handle all other preference categories as dynamic plain objects
  Object.keys(preferencesData).forEach(key => {
    if (key !== 'portfolio') {
      additionalPreferences[key] = preferencesData[key];
      console.log(`Including dynamic preference category: ${key}`);
    }
  });

  const updateData = new CreateCustomerPreferencesRequestDto(
    customerId,
    portfolioPreferences,
    additionalPreferences
  );

  // Execute use case
  const adapter = new UpdateCustomerPreferencesAdapter();
  const useCase = new UpdateCustomerPreferencesUseCase(adapter);

  const response = await useCase.execute(customerId, updateData, companyName);

  console.log(`Customer preferences updated successfully for ${companyName}`);
  console.log("Response:", JSON.stringify(response, null, 2));
}

updateCustomerPreferences(companyName).catch((error) => {
  console.error("\nError updating customer preferences:", error);
  process.exit(1);
});
