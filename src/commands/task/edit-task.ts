import * as path from "path";
import * as fs from "fs";
import * as yaml from "js-yaml";
import axios from "axios";
import * as dotenv from "dotenv";
import { fetch } from "../../common/http-client";
dotenv.config();

const args = process.argv.slice(2);

if (args.length < 2) {
  console.error("Usage: npm run task:edit <task-name> <company-name>");
  process.exit(1);
}

const extractArgValue = (arg: string): string | null => {
  const parts = arg.split("=");
  return parts.length === 2 ? parts[1] : null;
};

const taskName = extractArgValue(args[0]);
const companyName = extractArgValue(args[1]);

if (!taskName || !companyName) {
  console.error(
    "Invalid arguments. Expected format: task-name=<value> company-name=<value>"
  );
  process.exit(1);
}

if (!process.env.API_URL) {
  console.error("API_URL is not set in the environment variables.");
  process.exit(1);
}

function loadYamlFile(filePath: string): any {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }
  return yaml.load(fs.readFileSync(filePath, "utf8"));
}

async function fetchCustomerId(companyName: string): Promise<string> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/auth/accounts/nickname/${companyName}`
    );

    if (
      !response.data ||
      !response.data.data ||
      !response.data.data.customerId
    ) {
      throw new Error(
        "Customer ID not found for the provided company nickname."
      );
    }

    return response.data.data.customerId;
  } catch (error) {
    console.error("Error fetching customer ID:", error);
    throw new Error("Failed to fetch customer ID.");
  }
}

async function fetchTaskIdByName(taskName: string): Promise<string> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/intelligence/tasks/`
    );

    if (!response.data || !response.data.data) {
      throw new Error("Failed to fetch tasks.");
    }

    // Trim and compare names to avoid issues with spaces
    const task = response.data.data.find(
      (task: any) => task.name && task.name.trim() === taskName.trim()
    );

    if (!task) {
      throw new Error(`Task ${taskName} not found.`);
    }

    return task.id;
  } catch (error) {
    console.error("Error fetching task ID:", error);
    throw new Error("Failed to fetch task ID.");
  }
}

async function fetchAgentIdByName(agentName: string): Promise<string> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/intelligence/agents/`
    );

    if (!response.data || !response.data.data) {
      throw new Error("Failed to fetch agents.");
    }

    const agent = response.data.data.find(
      (agent: any) => agent.name && agent.name.trim() === agentName.trim()
    );

    if (!agent) {
      throw new Error(`Agent ${agentName} not found.`);
    }

    return agent.id;
  } catch (error) {
    console.error("Error fetching agent ID:", error);
    throw new Error("Failed to fetch agent ID.");
  }
}

async function updateTaskFromYaml(taskName: string, companyName: string) {
  try {
    const COMPANY_PATH = path.resolve(
      __dirname,
      `../../data/companies/${companyName}`
    );
    const tasksData = loadYamlFile(path.resolve(COMPANY_PATH, "tasks.yml"));

    const task = tasksData.tasks.find((task: any) => task.name === taskName);

    if (!task) {
      throw new Error(
        `Task ${taskName} not found in YAML for company ${companyName}.`
      );
    }

    const taskId = await fetchTaskIdByName(taskName);
    const agentId = await fetchAgentIdByName(task.agent);
    task.agent = agentId;

    const url = `${process.env.API_URL}/api/v1/intelligence/tasks/${taskId}`;
    const response = await fetch(companyName, url, "put", task);

    if (response.status !== 200) {
      throw new Error(`Failed to update task: ${response.statusText}`);
    }

    console.log(`Task ${taskName} updated successfully on the server.`);
  } catch (error) {
    console.error("Error updating task on server:", error);
    process.exit(1);
  }
}

updateTaskFromYaml(taskName, companyName);
