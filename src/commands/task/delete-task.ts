import axios from "axios";
import * as dotenv from "dotenv";
import { fetch } from "../../common/http-client";
dotenv.config();

const args = process.argv.slice(2);

if (args.length < 2) {
  console.error(
    "\nUsage: npm run task:delete task-name=<task-name> company-name=<company-name>"
  );
  process.exit(1);
}

const extractArgValue = (arg: string): string | null => {
  const parts = arg.split("=");
  return parts.length === 2 ? parts[1] : null;
};

const taskName = extractArgValue(args[0]);
const companyName = extractArgValue(args[1]);

if (!taskName || !companyName) {
  console.error(
    "\nInvalid arguments. Expected format: task-name=<value> company-name=<value>"
  );
  process.exit(1);
}

async function fetchTaskIdByName(taskName: string): Promise<string | null> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/intelligence/tasks/`
    );

    if (!response.data || !response.data.data) {
      throw new Error("Failed to fetch tasks.");
    }

    const task = response.data.data.find(
      (task: any) => task.name && task.name.trim() === taskName.trim()
    );

    if (!task) {
      console.log(`\nTask ${taskName} not found.`);
      return null;
    }

    return task.id;
  } catch (error) {
    console.error("\nError fetching task ID:", error);
    throw new Error("Failed to fetch task ID.");
  }
}

async function deleteTaskById(
  taskId: string,
  companyName: string
): Promise<void> {
  try {
    const url = `${process.env.API_URL}/api/v1/intelligence/tasks/${taskId}`;
    const response = await fetch(companyName, url, "delete");

    if (response.status === 200) {
      console.log(`Task ${taskName} deleted successfully.`);
    } else {
      console.error(
        `\nFailed to delete task: ${taskName}. Status: ${response.status}`
      );
    }
  } catch (error) {
    console.error("\nError deleting task:", error);
    throw new Error("Failed to delete task.");
  }
}

(async () => {
  try {
    const taskId = await fetchTaskIdByName(taskName);
    if (!taskId) {
      console.error(`\nTask with name ${taskName} not found.`);
      process.exit(1);
    }
    await deleteTaskById(taskId, companyName);
  } catch (error) {
    console.error("\nError in deletion process:", error);
    process.exit(1);
  }
})();
