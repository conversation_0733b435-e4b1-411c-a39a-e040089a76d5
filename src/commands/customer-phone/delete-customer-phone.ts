import axios from "axios";
import * as dotenv from "dotenv";
import { fetch } from "../../common/http-client";
dotenv.config();

const args = process.argv.slice(2);

if (args.length < 2) {
  console.error(
    "\nUsage: npm run customer-phone:delete customer-phone=<phone-number> company-name=<company-name>"
  );
  process.exit(1);
}

const extractArgValue = (arg: string): string | null => {
  const parts = arg.split("=");
  return parts.length === 2 ? parts[1] : null;
};

const phoneNumber = extractArgValue(args[0]);
const companyName = extractArgValue(args[1]);

if (!phoneNumber || !companyName) {
  console.error(
    "\nInvalid arguments. Expected format: customer-phone=<value> company-name=<value>"
  );
  process.exit(1);
}

async function fetchCustomerId(companyName: string): Promise<string> {
  try {
    const response = await axios.get(
      `${process.env.API_URL}/api/v1/auth/accounts/nickname/${companyName}`
    );

    if (
      !response.data ||
      !response.data.data ||
      !response.data.data.customerId
    ) {
      throw new Error(
        "Customer ID not found for the provided company nickname."
      );
    }

    return response.data.data.customerId;
  } catch (error) {
    console.error("\nError fetching customer ID:", error);
    throw new Error("Failed to fetch customer ID.");
  }
}

async function deleteCustomerPhone(customerId: string, phoneNumber: string) {
  try {
    const url = `${process.env.API_URL}/api/v1/message-hub/customers/${customerId}/phones/${phoneNumber}`;
    const response = await fetch(companyName!, url, "delete");

    if (response.status === 200) {
      console.log(`Customer phone ${phoneNumber} deleted successfully.`);
    } else {
      console.error(
        `\nFailed to delete customer phone ${phoneNumber}. Status: ${response.status}`
      );
    }
  } catch (error) {
    console.error("\nError deleting customer phone:", error);
    throw new Error("Failed to delete customer phone.");
  }
}

(async () => {
  try {
    const customerId = await fetchCustomerId(companyName);
    await deleteCustomerPhone(customerId, phoneNumber);
  } catch (error) {
    console.error("\nError in deletion process:", error);
    process.exit(1);
  }
})();
