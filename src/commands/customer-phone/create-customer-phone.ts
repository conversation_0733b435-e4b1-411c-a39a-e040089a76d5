import * as path from "path";
import { CreateCustomerPhoneAdapter } from "../../infrastructure/adapters/create-customer-phone.adapter";
import { CreateCustomerPhoneRequestDto } from "../../infrastructure/dtos/in/create-customer-phone-request.dto";
import * as fs from "fs";
import * as yaml from "js-yaml";
import axios from "axios";
import * as dotenv from "dotenv";
import { fetch } from "../../common/http-client";
dotenv.config();

// Argument verification
const args = process.argv.slice(2);

if (args.length < 1) {
  console.error("\nUsage: npm run customer-phones company=<company-name>");
  process.exit(1);
}

const companyName = args[0].split("=")[1];
if (!companyName) {
  console.error(
    "\nPlease provide a company name in the format npm run customer-phones company=<company-name>"
  );
  process.exit(1);
}

function loadYamlFile(filePath: string): any {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }
  return yaml.load(fs.readFileSync(filePath, "utf8"));
}

async function fetchCustomerId(companyName: string): Promise<string> {
  const response = await axios.get(
    `${process.env.API_URL}/api/v1/auth/accounts/nickname/${companyName}`
  );
  if (!response.data || !response.data.data || !response.data.data.customerId) {
    throw new Error("Customer ID not found for the given company nickname.");
  }
  return response.data.data.customerId;
}

async function fetchExistingPhoneNumbers(
  customerId: string
): Promise<string[]> {
  const response = await axios.get(
    `${process.env.API_URL}/api/v1/message-hub/customers/${customerId}/phones/`
  );
  if (!response.data || !response.data.data) {
    throw new Error("Failed to fetch existing phone numbers.");
  }
  return response.data.data.map((phone: any) => phone.phoneNumber);
}

async function createCustomerPhone(companyName: string) {
  const COMPANY_PATH = path.resolve(
    __dirname,
    `../../data/companies/${companyName}`
  );
  const customerData = loadYamlFile(
    path.resolve(COMPANY_PATH, "customer-phones.yml")
  );

  if (
    !customerData["customer-phones"] ||
    customerData["customer-phones"].length === 0
  ) {
    throw new Error("Customer phone details not found.");
  }

  const customerId = await fetchCustomerId(companyName);
  const existingPhoneNumbers = await fetchExistingPhoneNumbers(customerId);

  // Loop to create customer phones
  for (let index = 0; index < customerData["customer-phones"].length; index++) {
    const customerPhoneData = customerData["customer-phones"][index];

    if (!existingPhoneNumbers.includes(customerPhoneData.phoneNumber)) {
      const createData: CreateCustomerPhoneRequestDto = {
        customerId,
        ...customerPhoneData,
      };

      const url = `${process.env.API_URL}/api/v1/message-hub/customers/${customerId}/phones/`;
      const response = await fetch(companyName, url, "post", createData);

      console.log(
        `Customer phone ${customerPhoneData.phoneNumber} created successfully.`
      );
    } else {
      console.log(
        `Customer phone ${customerPhoneData.phoneNumber} already exists. Skipping creation.`
      );
    }
  }
}

createCustomerPhone(companyName).catch((error) => {
  console.error("\nError creating customer phone:", error);
  process.exit(1);
});
