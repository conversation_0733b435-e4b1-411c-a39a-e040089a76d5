export class CreatePortfolioResponseDto {
  status!: string;
  data!: any;
  followUpAfter!: number;
  maxFollowUps!: number;
  totalQuantity!: number;
  processedQuantity!: number;
  totalSuccessQuantity!: number;
  totalFailedQuantity!: number;
  executionStatus!: string;
  importStatus!: string;
  processingRateLimit!: number;
  portfolioStatus!: string;

  constructor(response: any) {
    this.status = response.status;
    this.data = response.data;
  }
}
