export class CompanySignupResponseDto {
  statusCode!: number;
  data!: {
    account: {
      id: string;
      name: string;
      role: string;
      status: string;
      customerId: string;
      createdAt: string;
      updatedAt: string;
    };
    customer: {
      name: string;
      cnpj: string;
      email: string;
      phone: string;
      whatsappPhone: string;
      segment: string;
      id: string;
    };
    user: {
      id: string;
      email: string;
      firstname: string;
      lastname: string;
      roleInAccount: string;
      accountId: string;
      status: string;
      createdAt: string;
      updatedAt: string;
    };
  };
}
