export class CreateWorkflowResponseDto {
  statusCode!: number;
  data!: {
    workflowId: string;
    name: string;
    description: string;
    status: string;
    steps: {
      stepId: string;
      description: string;
      order: number;
      middlewares: {
        id: string;
        taskId: string;
        name: string;
        description: string;
        type: string;
      }[];
    }[];
  };
}
