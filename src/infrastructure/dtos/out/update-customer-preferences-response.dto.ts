export class UpdatePortfolioPreferencesResponseDto {
  readonly defaultWorkflowId: string;
  readonly timezoneUTC: string;
  readonly importCronExpression: string;
  readonly followUpWorkflowId: string;
  readonly followUpCronExpression: string;
  readonly followUpQuantity: number;
  readonly followUpIntervalMinutes: number;
  readonly exportColumns?: string[];

  constructor(
    defaultWorkflowId: string,
    timezoneUTC: string,
    importCronExpression: string,
    followUpWorkflowId: string,
    followUpCronExpression: string,
    followUpQuantity: number,
    followUpIntervalMinutes: number,
    exportColumns?: string[],
  ) {
    this.defaultWorkflowId = defaultWorkflowId;
    this.timezoneUTC = timezoneUTC;
    this.importCronExpression = importCronExpression;
    this.followUpWorkflowId = followUpWorkflowId;
    this.followUpCronExpression = followUpCronExpression;
    this.followUpQuantity = followUpQuantity;
    this.followUpIntervalMinutes = followUpIntervalMinutes;
    this.exportColumns = exportColumns;
  }
}



export class UpdateCustomerPreferencesResponseDto {
  readonly statusCode: number;
  readonly data: {
    readonly customerId: string;
    readonly portfolio?: UpdatePortfolioPreferencesResponseDto;
    readonly [key: string]: any; // Allow dynamic properties
  };

  constructor(
    statusCode: number,
    customerId: string,
    preferences: {
      portfolio?: UpdatePortfolioPreferencesResponseDto;
      [key: string]: any;
    }
  ) {
    this.statusCode = statusCode;
    this.data = {
      customerId,
      ...preferences,
    };
  }
}
