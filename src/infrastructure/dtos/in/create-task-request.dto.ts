import { IsNotEmpty, <PERSON>Optional, IsString, IsUUID } from "class-validator";

export class CreateTaskRequestDto {
  @IsUUID("4")
  @IsOptional()
  id?: string;

  @IsString()
  @IsOptional()
  readonly name?: string;

  @IsString()
  @IsNotEmpty()
  readonly description: string;

  @IsString()
  @IsNotEmpty()
  readonly agent: string;

  @IsString()
  @IsOptional()
  readonly responseTemplate: string | null;

  @IsString()
  @IsOptional()
  readonly managerAgentId: string | null;

  @IsString()
  @IsOptional()
  readonly status?: string;

  constructor(
    description: string,
    name: string,
    agent: string,
    responseTemplate: string,
    managerAgentId: string,
    status: string
  ) {
    this.description = description;
    this.name = name;
    this.agent = agent;
    this.responseTemplate = responseTemplate;
    this.managerAgentId = managerAgentId;
    this.status = status;
  }
}
