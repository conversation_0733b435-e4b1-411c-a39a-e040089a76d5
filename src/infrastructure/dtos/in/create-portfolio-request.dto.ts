import {
  IsBoolean,
  <PERSON>Not<PERSON>mpty,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>al,
  IsString,
  Is<PERSON>UI<PERSON>,
  Matches,
} from "class-validator";

export class CreatePortfolioRequestDto {
  @IsUUID("4")
  @IsOptional()
  id?: string;

  @IsNotEmpty({ message: "name is required" })
  @IsString({ message: "name must be a string" })
  readonly name: string;

  @IsUUID("4")
  @IsNotEmpty()
  readonly workflowId: string;

  @IsNotEmpty({ message: "workExpression is required" })
  @Matches(
    /(@(annually|yearly|monthly|weekly|daily|hourly|reboot))|(@every (\d+(ns|us|µs|ms|s|m|h))+)|((((\d+,)+\d+|(\d+(\/|-)\d+)|\d+|\*) ?){5,7})/,
    { message: "workExpression must be a valid Cron expression." }
  )
  readonly workExpression: string;

  @IsNumber()
  @IsOptional()
  readonly idleAfter?: number;

  @IsBoolean()
  @IsOptional()
  readonly executeImmediately?: boolean = false;

  @IsBoolean()
  readonly isDefault: boolean = false;

  constructor(
    name: string,
    workflowId: string,
    workExpression: string,
    idleAfter?: number,
    executeImmediately?: boolean,
    isDefault?: boolean
  ) {
    this.name = name;
    this.workflowId = workflowId;
    this.workExpression = workExpression;
    this.idleAfter = idleAfter;
    this.executeImmediately = executeImmediately ?? false;
    this.isDefault = isDefault ?? false;
  }
}
