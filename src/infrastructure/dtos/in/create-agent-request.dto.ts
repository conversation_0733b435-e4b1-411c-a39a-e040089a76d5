import { IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";

export class CreateAgentRequestDto {
  @IsUUID("4")
  @IsOptional()
  id?: string;

  @IsString()
  @IsNotEmpty()
  role: string;

  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsNotEmpty()
  backstory: string;

  @IsString()
  @IsNotEmpty()
  llmModel: string;

  @IsString()
  @IsNotEmpty()
  outputType: string;

  @IsString()
  @IsNotEmpty()
  lang: string;

  @IsOptional()
  voice: null | string;

  @IsOptional()
  status?: string;

  constructor(
    role: string,
    backstory: string,
    llm: string,
    outputType: string,
    lang: string,
    voice: string,
    status: string,
    name?: string
  ) {
    this.role = role;
    this.backstory = backstory;
    this.llmModel = llm;
    this.outputType = outputType;
    this.lang = lang;
    this.voice = voice;
    this.status = status;
    this.name = name;
  }
}
