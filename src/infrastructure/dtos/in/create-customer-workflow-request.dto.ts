import {
  IsDate,
  IsNotEmpty,
  <PERSON>Optional,
  IsString,
  IsUUID,
} from "class-validator";

export class MiddlewareDto {
  taskName!: string;
  taskId!: string;
  name!: string;
  description!: string;
  type!: string;
}

export class WorkflowStepDto {
  description!: string;
  order!: number;
  taskId!: string;
  params!: Record<string, any>;
  middlewares?: MiddlewareDto[];
}

export class CreateWorkflowRequestDto {
  @IsUUID("4")
  @IsOptional()
  readonly id?: string;

  @IsUUID("4")
  @IsNotEmpty()
  readonly customerId: string;

  @IsUUID("4")
  @IsNotEmpty()
  readonly workflowId: string;

  @IsString()
  @IsNotEmpty()
  readonly workflowName: string;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    customerId: string,
    workflowId: string,
    workflowName: string,
    createdAt: Date,
    updatedAt: Date
  ) {
    this.id = id;
    this.customerId = customerId;
    this.workflowId = workflowId;
    this.workflowName = workflowName;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
