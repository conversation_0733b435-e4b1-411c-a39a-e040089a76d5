import { IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";

export class CreatePortfolioStatusMappingRequestDto {
  @IsUUID("4")
  @IsNotEmpty({ message: "workflowId is required" })
  readonly workflowId: string;

  @IsNotEmpty({ message: "postExecutionResponse is required" })
  @IsString({ message: "postExecutionResponse must be a string" })
  readonly postExecutionResponse: string;

  @IsNotEmpty({ message: "responseKey is required" })
  @IsString({ message: "responseKey must be a string" })
  readonly responseKey: string;

  @IsNotEmpty({ message: "portfolioItemStatus is required" })
  @IsString({ message: "portfolioItemStatus must be a string" })
  readonly portfolioItemStatus: string;

  @IsOptional()
  readonly createdAt?: Date;

  @IsOptional()
  readonly updatedAt?: Date;

  constructor(
    workflowId: string,
    postExecutionResponse: string,
    responseKey: string,
    portfolioItemStatus: string,
    createdAt?: Date,
    updatedAt?: Date
  ) {
    this.workflowId = workflowId;
    this.portfolioItemStatus = portfolioItemStatus;
    this.postExecutionResponse = postExecutionResponse;
    this.responseKey = responseKey;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
