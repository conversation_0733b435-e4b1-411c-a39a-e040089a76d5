import { IsE<PERSON>, <PERSON>NotEmpty, <PERSON>N<PERSON>ber, IsString } from "class-validator";

export class CreateCustomerPhoneRequestDto {
  @IsString()
  @IsNotEmpty()
  customerId: string;

  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @IsString()
  apiUrl: string;

  @IsString()
  @IsNotEmpty()
  incomingCron: string;

  @IsString()
  @IsNotEmpty()
  outgoingCron: string;

  @IsNumber()
  @IsNotEmpty()
  outgoingMaxDelay: number;

  @IsNumber()
  @IsNotEmpty()
  weight: number;

  constructor(
    customerId: string,
    phoneNumber: string,
    apiUrl: string,
    incomingCron: string,
    outgoingCron: string,
    outgoingMaxDelay: number,
    weight: number
  ) {
    this.customerId = customerId;
    this.phoneNumber = phoneNumber;
    this.apiUrl = apiUrl;
    this.incomingCron = incomingCron;
    this.outgoingCron = outgoingCron;
    this.outgoingMaxDelay = outgoingMaxDelay;
    this.weight = weight;
  }
}
