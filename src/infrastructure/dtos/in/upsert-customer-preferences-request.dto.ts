import {
  IsUUID,
  IsNotEmpty,
  IsString,
  IsArray,
  IsNumber,
  IsPositive,
  ValidateNested,
  Matches,
  IsOptional,
  IsObject,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum StatsDataSource {
  CUSTOM_DATA = 'customData',
  MIDDLEWARE = 'middleware',
}

export class StatsFieldConfigDto {
  @IsEnum(StatsDataSource, { message: 'source must be either customData or middleware' })
  @IsNotEmpty()
  readonly source: StatsDataSource;

  @IsString({ message: 'path must be a string' })
  @IsNotEmpty()
  readonly path: string;

  constructor(source: StatsDataSource, path: string) {
    this.source = source;
    this.path = path;
  }
}

export class StatsConfigDto {
  @IsString({ message: 'workflowId must be a string' })
  @IsNotEmpty()
  readonly workflowId: string;

  // Allow dynamic statistical field properties while maintaining type safety
  // Each property (except workflowId) should follow the StatsFieldConfigDto structure
  // Examples: recoveredValueFrom, currentDebit, originalDebt, etc.
  [key: string]: StatsFieldConfigDto | string;

  constructor(workflowId: string, ...dynamicFields: StatsFieldConfigDto[]) {
    this.workflowId = workflowId;

    // Handle dynamic statistical fields
    if (dynamicFields && dynamicFields.length > 0) {
      Object.assign(this, dynamicFields[0]);
    }
  }
}

export class ExportConfigDto {
  @IsString({ message: 'workflowName must be a string' })
  @IsNotEmpty()
  readonly workflowName: string;

  // Allow dynamic export field properties while maintaining type safety
  // Each property (except workflowName) should follow the ExportFieldConfigDto structure
  // Examples: "Portfolio Name", "Status", "Phone Number", etc.
  [key: string]: ExportFieldConfigDto | string;

  constructor(workflowName: string, dynamicFields?: Record<string, ExportFieldConfigDto>) {
    this.workflowName = workflowName;

    // Handle dynamic export fields
    if (dynamicFields) {
      Object.assign(this, dynamicFields);
    }
  }
}

export enum ExportDataSource {
  PORTFOLIO = 'portfolio',
  PORTFOLIO_ITEM = 'portfolio-item',
  CUSTOM_DATA = 'customData',
  MIDDLEWARE = 'middleware',
}

export class ExportFieldConfigDto {
  @IsEnum(ExportDataSource, {
    message: 'source must be one of: portfolio, portfolio-item, customData, middleware'
  })
  @IsNotEmpty()
  readonly source: ExportDataSource;

  @IsString({ message: 'path must be a string' })
  @IsNotEmpty()
  readonly path: string;

  @IsString({ message: 'format must be a string' })
  @IsOptional()
  readonly format?: string;

  constructor(source: ExportDataSource, path: string, format?: string) {
    this.source = source;
    this.path = path;
    this.format = format;
  }
}

export class TaxRulesDto {
  @IsString({ message: 'penaltyFee must be a string' })
  @IsOptional()
  readonly penaltyFee?: string;

  @IsString({ message: 'dailyFee must be a string' })
  @IsOptional()
  readonly dailyFee?: string;

  constructor(penaltyFee?: string, dailyFee?: string) {
    this.penaltyFee = penaltyFee;
    this.dailyFee = dailyFee;
  }
}

export class CustomImportConfigDto {
  @IsString({ message: 'delimiter must be a string' })
  @IsOptional()
  readonly delimiter?: string;

  @ValidateNested({ message: 'taxRules must be valid' })
  @Type(() => TaxRulesDto)
  @IsOptional()
  readonly taxRules?: TaxRulesDto;

  @IsObject({ message: 'headerMapping must be an object' })
  @IsOptional()
  readonly headerMapping?: Record<string, string>;

  @IsArray({ message: 'additionalHeaders must be an array' })
  @IsString({ each: true, message: 'Each post-processing header must be a string' })
  @IsOptional()
  readonly additionalHeaders?: string[];

  constructor(
    delimiter?: string,
    taxRules?: TaxRulesDto,
    headerMapping?: Record<string, string>,
    additionalHeaders?: string[],
  ) {
    this.delimiter = delimiter;
    this.taxRules = taxRules;
    this.headerMapping = headerMapping;
    this.additionalHeaders = additionalHeaders;
  }
}

export class PortfolioPreferencesRequestDto {
  @IsUUID('4', { message: 'defaultWorkflowId must be a valid UUID' })
  @IsNotEmpty({ message: 'defaultWorkflowId is required' })
  readonly defaultWorkflowId?: string;

  @IsString({ message: 'timezoneUTC must be a string' })
  @IsNotEmpty({ message: 'timezoneUTC is required' })
  @Matches(/^[+-]?\d+(\.\d+)?$/, {
    message: 'timezoneUTC must be a valid timezone offset (e.g., "-3", "+5.5")',
  })
  readonly timezoneUTC?: string;

  @IsString({ message: 'importCronExpression must be a string' })
  @IsNotEmpty({ message: 'importCronExpression is required' })
  readonly importCronExpression?: string;

  @IsUUID('4', { message: 'followUpWorkflowId must be a valid UUID' })
  @IsNotEmpty({ message: 'followUpWorkflowId is required' })
  readonly followUpWorkflowId?: string;

  @IsString({ message: 'followUpCronExpression must be a string' })
  @IsNotEmpty({ message: 'followUpCronExpression is required' })
  readonly followUpCronExpression?: string;

  @IsNumber({}, { message: 'followUpQuantity must be a number' })
  @IsPositive({ message: 'followUpQuantity must be a positive number' })
  readonly followUpQuantity?: number;

  @IsNumber({}, { message: 'followUpIntervalMinutes must be a number' })
  @IsPositive({ message: 'followUpIntervalMinutes must be a positive number' })
  readonly followUpIntervalMinutes?: number;

  @IsArray({ message: 'exportColumns must be an array' })
  @IsString({ each: true, message: 'Each export column must be a string' })
  @IsOptional()
  readonly exportColumns?: string[];

  @Type(() => CustomImportConfigDto)
  @IsOptional()
  readonly customImportConfig?: CustomImportConfigDto;

  @IsArray({ message: 'statsConfig must be an array' })
  @ValidateNested({ each: true, message: 'Each statsConfig item must be valid' })
  @Type(() => StatsConfigDto)
  @IsOptional()
  readonly statsConfig?: StatsConfigDto[];

  @IsArray({ message: 'exportConfig must be an array' })
  @ValidateNested({ each: true, message: 'Each exportConfig item must be valid' })
  @Type(() => ExportConfigDto)
  @IsOptional()
  readonly exportConfig?: ExportConfigDto[];

  constructor(
    defaultWorkflowId?: string,
    timezoneUTC?: string,
    importCronExpression?: string,
    followUpWorkflowId?: string,
    followUpCronExpression?: string,
    followUpQuantity?: number,
    followUpIntervalMinutes?: number,
    exportColumns?: string[],
    customImportConfig?: CustomImportConfigDto,
    statsConfig?: StatsConfigDto[],
    exportConfig?: ExportConfigDto[],
  ) {
    this.defaultWorkflowId = defaultWorkflowId;
    this.timezoneUTC = timezoneUTC;
    this.importCronExpression = importCronExpression;
    this.followUpWorkflowId = followUpWorkflowId;
    this.followUpCronExpression = followUpCronExpression;
    this.followUpQuantity = followUpQuantity;
    this.followUpIntervalMinutes = followUpIntervalMinutes;
    this.exportColumns = exportColumns || [];
    this.customImportConfig = customImportConfig;
    this.statsConfig = statsConfig;
    this.exportConfig = exportConfig;
  }
}

export class CreateCustomerPreferencesRequestDto {
  @IsString({ message: 'customerId must be a string' })
  @IsNotEmpty({ message: 'customerId is required' })
  readonly customerId: string;

  @ValidateNested({ message: 'portfolio preferences must be valid' })
  @Type(() => PortfolioPreferencesRequestDto)
  @IsNotEmpty({ message: 'portfolio preferences are required' })
  readonly portfolio: PortfolioPreferencesRequestDto;

  // Allow additional dynamic properties
  [key: string]: any;

  constructor(
    customerId: string,
    portfolio: PortfolioPreferencesRequestDto,
    additionalPreferences: {
      [key: string]: any;
    } = {}
  ) {
    this.customerId = customerId;
    this.portfolio = portfolio;

    // Assign any additional dynamic properties
    Object.keys(additionalPreferences).forEach(key => {
      (this as any)[key] = additionalPreferences[key];
    });
  }
}
