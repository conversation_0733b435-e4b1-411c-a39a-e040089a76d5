import {
  Is<PERSON>mail,
  <PERSON>Not<PERSON>mpty,
  <PERSON><PERSON><PERSON>al,
  IsPhoneNumber,
  IsString,
  ValidateNested,
} from "class-validator";
import { Type } from "class-transformer";

class UserData {
  @IsEmail()
  @IsNotEmpty()
  email!: string;

  @IsString()
  @IsNotEmpty()
  firstname!: string;

  @IsString()
  @IsNotEmpty()
  lastname!: string;

  @IsString()
  @IsNotEmpty()
  password!: string;

  @IsString()
  @IsNotEmpty()
  passwordConfirmation!: string;
}

class CustomerData {
  @IsNotEmpty({ message: "name is required" })
  @IsString({ message: "name must be a string" })
  name!: string;

  @IsNotEmpty({ message: "cnpj is required" })
  @IsString({ message: "cnpj must be a string" })
  cnpj!: string;

  @IsOptional()
  @IsEmail({}, { message: "email must be a valid email" })
  email?: string;

  @IsNotEmpty({ message: "phone is required" })
  @IsPhoneNumber("BR", { message: "phone must be a valid phone number" })
  phone!: string;

  @IsNotEmpty({ message: "whatsappPhone is required" })
  @IsPhoneNumber("BR", {
    message: "whatsappPhone must be a valid phone number",
  })
  whatsappPhone!: string;

  @IsNotEmpty({ message: "segment is required" })
  @IsString({ message: "segment must be a string" })
  segment!: string;
}

class AccountData {
  @IsString()
  @IsNotEmpty()
  nickname!: string;
}

export class CompanySignupRequestDto {
  @ValidateNested()
  @Type(() => CustomerData)
  customerData!: CustomerData;

  @ValidateNested()
  @Type(() => AccountData)
  accountData!: AccountData;

  @ValidateNested()
  @Type(() => UserData)
  userData!: UserData;
}
