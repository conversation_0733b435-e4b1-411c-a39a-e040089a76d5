import { CreateCustomerPhoneRequestDto } from "../../dtos/in/create-customer-phone-request.dto";
import { CreateCustomerPhoneResponseDto } from "../../dtos/out/create-customer-phone-response.dto";
import { AssociateCustomerPhoneResponseDto } from "../../dtos/out/associate-customer-phone-response.dto";

export interface CreateCustomerPhonePort {
  create(
    data: CreateCustomerPhoneRequestDto,
    companyName: string
  ): Promise<CreateCustomerPhoneResponseDto>;
}
