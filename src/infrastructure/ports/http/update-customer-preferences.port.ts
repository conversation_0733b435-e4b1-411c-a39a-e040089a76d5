import { CreateCustomerPreferencesRequestDto } from "../../dtos/in/upsert-customer-preferences-request.dto";
import { UpdateCustomerPreferencesResponseDto } from "../../dtos/out/update-customer-preferences-response.dto";

export interface UpdateCustomerPreferencesPort {
  update(
    customerId: string,
    data: CreateCustomerPreferencesRequestDto,
    companyName: string
  ): Promise<UpdateCustomerPreferencesResponseDto>;
}
