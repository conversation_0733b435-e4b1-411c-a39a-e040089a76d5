import { CreateWorkflowRequestDto } from "../../dtos/in/create-customer-workflow-request.dto";
import { CreateWorkflowResponseDto } from "../../dtos/out/create-customer-workflow-response.dto";
import { GetCustomerWorkflowsResponseDto } from "../../dtos/out/get-customer-workflows-response.dto";

export interface CreateWorkflowPort {
  create(
    data: CreateWorkflowRequestDto,
    companyName: string
  ): Promise<CreateWorkflowResponseDto>;
  associateWorkflow(
    customerId: string,
    workflowId: string,
    companyName: string
  ): Promise<GetCustomerWorkflowsResponseDto>;
}
