import { CreateCustomerPreferencesRequestDto } from "../../dtos/in/upsert-customer-preferences-request.dto";
import { CreateCustomerPreferencesResponseDto } from "../../dtos/out/create-customer-preferences-response.dto";

export interface CreateCustomerPreferencesPort {
  create(
    data: CreateCustomerPreferencesRequestDto,
    companyName: string
  ): Promise<CreateCustomerPreferencesResponseDto>;
}
