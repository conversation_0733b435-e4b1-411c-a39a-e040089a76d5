import { CreatePortfolioStatusMappingRequestDto } from "../../dtos/in/create-portfolio-item-status-mapping-request.dto";
import {
  CreatePortfolioStatusMappingResponseDto,
  AssociateWorkflowResponseDto,
} from "../../dtos/out/create-portfolio-item-status-mapping-response.dto";

export interface CreatePortfolioStatusMappingPort {
  create(
    data: CreatePortfolioStatusMappingRequestDto,
    companyName: string
  ): Promise<CreatePortfolioStatusMappingResponseDto>;

  associateWorkflow(
    workflowId: string,
    customerId: string,
    companyName: string
  ): Promise<AssociateWorkflowResponseDto>;
}
