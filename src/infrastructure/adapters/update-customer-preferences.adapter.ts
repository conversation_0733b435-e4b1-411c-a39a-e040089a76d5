import { AxiosError } from "axios";
import { UpdateCustomerPreferencesPort } from "../ports/http/update-customer-preferences.port";
import { UpdateCustomerPreferencesResponseDto } from "../dtos/out/update-customer-preferences-response.dto";
import { fetch } from "../../common/http-client";
import { CreateCustomerPreferencesRequestDto } from "../dtos/in/upsert-customer-preferences-request.dto";

export class UpdateCustomerPreferencesAdapter implements UpdateCustomerPreferencesPort {
  async update(
    customerId: string,
    data: CreateCustomerPreferencesRequestDto,
    companyName: string
  ): Promise<UpdateCustomerPreferencesResponseDto> {
    try {
      const response = await fetch(
        companyName,
        `${process.env.API_URL}/api/v1/business-base/customer-preferences/${customerId}`,
        "put",
        data
      );

      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw error?.response?.data;
      }
      throw error;
    }
  }
}
