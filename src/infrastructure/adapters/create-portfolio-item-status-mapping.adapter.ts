import axios, { AxiosError } from "axios";
import { CreatePortfolioStatusMappingRequestDto } from "../dtos/in/create-portfolio-item-status-mapping-request.dto";
import {
  CreatePortfolioStatusMappingResponseDto,
  AssociateWorkflowResponseDto,
} from "../dtos/out/create-portfolio-item-status-mapping-response.dto";
import { CreatePortfolioStatusMappingPort } from "../ports/http/create-portfolio-item-status-mapping.port";
import { fetch } from "../../common/http-client";

export class CreatePortfolioStatusMappingAdapter
  implements CreatePortfolioStatusMappingPort
{
  public async create(
    data: CreatePortfolioStatusMappingRequestDto,
    companyName: string
  ): Promise<CreatePortfolioStatusMappingResponseDto> {
    try {
      const response = await fetch(
        companyName,
        `${process.env.API_URL}/api/v1/business-base/portfolio/items/status-mapping`,
        "post",
        data
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response) {
          console.error("Request failed with status:", error.response.status);
          console.error("Response data:", error.response.data);
        } else if (error.request) {
          console.error("No response received:", error.request);
        } else {
          console.error("Error setting up request:", error.message);
        }
      }
      throw error;
    }
  }

  public async associateWorkflow(
    workflowId: string,
    customerId: string,
    companyName: string
  ): Promise<AssociateWorkflowResponseDto> {
    try {
      const response = await fetch(
        companyName,
        `${process.env.API_URL}/api/v1/business-base/customers/${customerId}/workflows/${workflowId}`,
        "post"
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response) {
          console.error("Request failed with status:", error.response.status);
          console.error("Response data:", error.response.data);
        } else if (error.request) {
          console.error("No response received:", error.request);
        } else {
          console.error("Error setting up request:", error.message);
        }
      }
      throw error;
    }
  }
}
