import axios, { AxiosError } from "axios";
import { CreateAgentPort } from "../ports/http/create-agent.port";
import { CreateAgentRequestDto } from "../dtos/in/create-agent-request.dto";
import { CreateAgentResponseDto } from "../dtos/out/create-agent-response.dto";
import http from "http";
import { fetch } from "../../common/http-client";

export class CreateAgentAdapter implements CreateAgentPort {
  async create(
    data: CreateAgentRequestDto,
    companyName: string
  ): Promise<CreateAgentResponseDto> {
    try {
      const response = await fetch(
        companyName,
        `${process.env.API_URL}/api/v1/intelligence/agents`,
        "post",
        data
      );

      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response) {
          console.error("Request failed with status:", error.response.status);
          console.error("Response data:", error.response.data);
        } else if (error.request) {
          console.error("No response received:", error.request);
        } else {
          console.error("Error setting up request:", error.message);
        }
      }
      throw error;
    }
  }
}
