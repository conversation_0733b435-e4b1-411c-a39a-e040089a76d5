import axios, { AxiosError } from "axios";
import { LoginPort } from "../ports/http/login.port";
import { LoginRequestDto } from "../dtos/in/login-request.dto";
import { LoginResponseDto } from "../dtos/out/login-response.dto";
import { fetch } from "../../common/http-client";

export class LoginAdapter implements LoginPort {
  async login(
    data: LoginRequestDto,
    companyName: string
  ): Promise<LoginResponseDto> {
    try {
      const response = await fetch(
        companyName,
        `${process.env.API_URL}/api/v1/auth/session/login`,
        "post",
        data
      );

      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response) {
          console.error("Request failed with status:", error.response.status);
          console.error("Response data:", error.response.data);
        } else if (error.request) {
          console.error("No response received:", error.request);
        } else {
          console.error("Error setting up request:", error.message);
        }
      }
      throw error;
    }
  }
}
