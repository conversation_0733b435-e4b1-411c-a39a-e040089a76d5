import { fetch } from "../../common/http-client";
import { CreateCustomerPhonePort } from "../ports/http/create-customer-phone.port";
import { CreateCustomerPhoneRequestDto } from "../dtos/in/create-customer-phone-request.dto";
import { CreateCustomerPhoneResponseDto } from "../dtos/out/create-customer-phone-response.dto";
import { AxiosError } from "axios";

export class CreateCustomerPhoneAdapter implements CreateCustomerPhonePort {
  async create(
    data: CreateCustomerPhoneRequestDto,
    companyName: string
  ): Promise<CreateCustomerPhoneResponseDto> {
    try {
      const { customerId, ...phoneData } = data;

      const response = await fetch(
        companyName,
        `${process.env.API_URL}/api/v1/message-hub/customers/${customerId}/phones`,
        "post",
        phoneData
      );

      if (response.data.statusCode !== 201) {
        throw new Error("Phone was not created");
      }

      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response) {
          console.error("Request failed with status:", error.response.status);
          console.error("Response data:", error.response.data);
        } else if (error.request) {
          console.error("No response received:", error.request);
        } else {
          console.error("Error setting up request:", error.message);
        }
      }
      throw error;
    }
  }
}
