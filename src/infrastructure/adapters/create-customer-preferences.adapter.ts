import { AxiosError } from "axios";
import { CreateCustomerPreferencesPort } from "../ports/http/create-customer-preferences.port";
import { CreateCustomerPreferencesRequestDto } from "../dtos/in/upsert-customer-preferences-request.dto";
import { CreateCustomerPreferencesResponseDto } from "../dtos/out/create-customer-preferences-response.dto";
import { fetch } from "../../common/http-client";

export class CreateCustomerPreferencesAdapter implements CreateCustomerPreferencesPort {
  async create(
    data: CreateCustomerPreferencesRequestDto,
    companyName: string
  ): Promise<CreateCustomerPreferencesResponseDto> {
    try {
      const { customerId, ...preferencesData } = data;

      const response = await fetch(
        companyName,
        `${process.env.API_URL}/api/v1/business-base/customer-preferences/${customerId}`,
        "post",
        preferencesData
      );

      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw error?.response?.data;
      }
      throw error;
    }
  }
}
