import { fetch } from "../../common/http-client";
import { GetCustomerWorkflowPort } from "../ports/http/get-customer-workflow.port";
import { AxiosError } from "axios";

export class GetCustomerWorkflowAdapter implements GetCustomerWorkflowPort {
  async getWorkflow(
    customerId: string,
    workflowId: string,
    companyName: string
  ): Promise<any> {
    try {
      const response = await fetch(
        companyName,
        `${process.env.API_URL}/api/v1/workflows/${workflowId}/customers/${customerId}`,
        "get"
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response) {
          console.error("Request failed with status:", error.response.status);
          console.error("Response data:", error.response.data);
        } else if (error.request) {
          console.error("No response received:", error.request);
        } else {
          console.error("Error setting up request:", error.message);
        }
      }
      throw error;
    }
  }
}
