import axios, { AxiosError } from "axios";
import { CreateWorkflowPort } from "../ports/http/create-customer-workflow.port";
import { CreateWorkflowRequestDto } from "../dtos/in/create-customer-workflow-request.dto";
import { CreateWorkflowResponseDto } from "../dtos/out/create-customer-workflow-response.dto";
import { GetCustomerWorkflowsResponseDto } from "../dtos/out/get-customer-workflows-response.dto";
import { fetch } from "../../common/http-client";

export class CreateWorkflowAdapter implements CreateWorkflowPort {
  async create(
    data: CreateWorkflowRequestDto,
    companyName: string
  ): Promise<CreateWorkflowResponseDto> {
    try {
      const response = await fetch(
        companyName,
        `${process.env.API_URL}/api/v1/orchestrator/workflows`,
        "post",
        data
      );

      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response) {
          console.error("Request failed with status:", error.response.status);
          console.error("Response data:", error.response.data);
        } else if (error.request) {
          console.error("No response received:", error.request);
        } else {
          console.error("Error setting up request:", error.message);
        }
      }
      throw error;
    }
  }

  async associateWorkflow(
    customerId: string,
    workflowId: string,
    companyName: string
  ): Promise<GetCustomerWorkflowsResponseDto> {
    try {
      const response = await fetch(
        companyName,
        `${process.env.API_URL}/api/v1/business-base/customers/${customerId}/workflows/${workflowId}`,
        "post"
      );

      console.log(
        "\nWorkflow Association Response:",
        response.data.data.workflowName
      );

      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response) {
          console.error("Request failed with status:", error.response.status);
          console.error("Response data:", error.response.data);
        } else if (error.request) {
          console.error("No response received:", error.request);
        } else {
          console.error("Error setting up request:", error.message);
        }
      }
      throw error;
    }
  }
}
