import axios, { AxiosError } from "axios";
import { AccountSignupPort } from "../ports/http/account-signup.port";
import { CompanySignupRequestDto } from "../dtos/in/company-signup-request.dto";
import { CompanySignupResponseDto } from "../dtos/out/company-signup-response.dto";

export class AccountSignupAdapter implements AccountSignupPort {
  async signup(
    data: CompanySignupRequestDto
  ): Promise<CompanySignupResponseDto> {
    try {
      const response = await axios.post<CompanySignupResponseDto>(
        `${process.env.API_URL}/api/v1/auth/users/signup`,
        data,
        {
          headers: {
            "Content-Type": "application/json",
          },
          timeout: 10000,
          httpAgent: new (require("http").Agent)({ keepAlive: false }),
          httpsAgent: new (require("https").Agent)({ keepAlive: false }),
        }
      );

      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response) {
          console.error("Request failed with status:", error.response.status);
          console.error("Response data:", error.response.data);
        } else if (error.request) {
          console.error("No response received:", error.request);
        } else {
          console.error("Error setting up request:", error.message);
        }
      }
      throw error;
    }
  }
}
