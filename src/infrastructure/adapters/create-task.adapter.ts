import axios, { AxiosError } from "axios";
import { CreateTaskPort } from "../ports/http/create-task.port";
import { CreateTaskRequestDto } from "../dtos/in/create-task-request.dto";
import { CreateTaskResponseDto } from "../dtos/out/create-task-response.dto";
import { fetch } from "../../common/http-client";

export class CreateTaskAdapter implements CreateTaskPort {
  async create(
    data: CreateTaskRequestDto,
    companyName: string
  ): Promise<CreateTaskResponseDto> {
    try {
      const response = await fetch(
        companyName,
        `${process.env.API_URL}/api/v1/intelligence/tasks`,
        "post",
        data
      );

      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response) {
          console.error("Request failed with status:", error.response.status);
          console.error("Response data:", error.response.data);
        } else if (error.request) {
          console.error("No response received:", error.request);
        } else {
          console.error("Error setting up request:", error.message);
        }
      }
      throw error;
    }
  }
}
