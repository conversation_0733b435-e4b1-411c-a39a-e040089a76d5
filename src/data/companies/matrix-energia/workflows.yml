workflows:
  - name: "matrix-energia-negociador-divida-v1"
    description: "Negociador de Dívidas Matrix Energia"
    steps:
      - description: "Negociar dívida"
        order: 1
        task: matrix-energia-negociador-divida
        params:
        middlewares:
          - task: matrix-energia-obter-status-negociacao
            name: "interaction-status"
            description: "Extrair o status da negociação da conversa fornecida em formato JSON e o output deve ser ON_GOING ou ACCEPTED"
            type: "POST_EXECUTION"
            showOff: false
          - name: "resumo-negociacao"
            task: matrix-energia-gerar-resumo-negociacao
            description: "Middleware que extrai resumo da negociação"
            type: "POST_EXECUTION"
            showOff: true

  - name: matrix-negociador-divida-follow-up-v1
    description: "Follow up da dívida, datas futuras, tempo de resposta e lembrete de pagamento"
    steps:
      - description: "Follow up da dívida"
        order: 1
        task: matrix-negociador-divida-v4-follow-up
        middlewares:
          - task: matrix-negociador-dividas-follow-up-status
            name: mustFollow
            description: Verificar se deve ser feito o follow up com base no histórico da
              conversa fornecida em formato JSON e o output deve ser DONT_FOLLOW_UP ou FOLLOW_UP
            type: POST_EXECUTION
            showOff: true