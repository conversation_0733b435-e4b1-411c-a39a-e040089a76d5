portfolio:
  customImportConfig:
    delimiter: ";"
    additionalHeaders: [VALOR_TOTAL_CORRIGIDO]
    taxRules:
      penaltyFee: 2
      dailyFee: 0.0333
  statsConfig:
    - workflowName: "matrix-energia-negociador-divida-v1"
      dealValue:
        source: "customData"
        path: "VALOR_TOTAL_CORRIGIDO"
      originalDebt:
        source: "customData"
        path: "VALOR_ORIGINAL_DA_DIVIDA"
  exportConfig:
    - workflowName: "matrix-energia-negociador-divida-v1"
      "Portfolio Name": 
        source: "portfolio"
        path: "name"
      "Status":
        source: "portfolio-item"
        path: "current_status"
      "Phone Number":
        source: "portfolio-item"
        path: "phone_number"
      "Valor Original da Divida":
        source: "customData"
        path: "VALOR_DIVIDA_ORIGINAL"
      "Referencia da Divida":
        source: "customData"
        path: "REFERENCIA_DIVIDA"
      "Nome do Cliente":
        source: "customData"
        path: "NOME_DO_CLIENTE"
      "CNPJ ou CPF do Cliente":
        source: "customData"
        path: "CNPJ_CPF"
      "Nome da Distribuidora":
        source: "customData"
        path: "NOME_DA_DISTRIBUIDORA"
      "Número da Instalação":
        source: "customData"
        path: "NUMERO_DA_INSTALACAO"
      "Resumo da negociação":
        source: "middleware"
        path: "[resumo-negociacao].resumo"
      "Data do Acionamento":
        source: "portfolio-item"
        path: "sent_at"
        format: "data_hora"
status: "ACTIVE"
