workflows:
  - name: tmb-educacao-negotiation-workflow-v1
    description: "Negociador de Dívidas TMB Educação v1"
    steps:
      - description: "Negotiate debt"
        order: 1
        task: tmb-educacao-negotiation-task-v1
        params:
        middlewares:
          - name: "interaction-status"
            task: tmb-educacao-interaction-status-v1
            description: "Middleware que extrai o status da interação"
            type: "POST_EXECUTION"
            showOff: false

  - name: tmb-negociador-divida-follow-up-v1
    description: "Follow up da dívida, datas futuras, tempo de resposta e lembrete de pagamento"
    steps:
      - description: "Follow up da dívida"
        order: 1
        task: tmb-negociador-divida-v4-follow-up
        middlewares:
          - task: tmb-negociador-dividas-follow-up-status
            name: mustFollow
            description: Verificar se deve ser feito o follow up com base no histórico da
              conversa fornecida em formato JSON e o output deve ser DONT_FOLLOW_UP ou FOLLOW_UP
            type: POST_EXECUTION
            showOff: true