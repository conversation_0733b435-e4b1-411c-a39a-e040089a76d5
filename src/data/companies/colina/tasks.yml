tasks:
  - name: colina-negotiation-task
    agent: colina-debt-negotiator
    description: |
      Conduza a conversa seguindo o roteiro de mensagens definido.

  - name: colina-negotiation-analysis
    agent: colina-iteration-status-agent
    description: |
      Com base no histórico de negociação fornecido em formato JSON, onde o Usuário é o devedor, analise a conversa para extrair o status da negociação. 
      O status da negociação pode ser: \"ON_GOING\" ou \"ACCEPTED\". 
      - "ON_GOING": Significa que a negociação ainda está em andamento. 
      - "ACCEPTED": Significa que o devedor aceitou a proposta de pagamento. 
      
      A resposta deve ser **somente um JSON válido** que contenha o status da negociação. Retorne apenas o JSON no formato: {\"value\": \"ON_GOING\"} ou {\"value\": \"ACCEPTED\"}. **Não** inclua textos adicionais por exemplo \"```json\".
      
      {{conversationHistory}} "
    responseTemplate: |
      {\"value\": \"[ON_GOING ou ACCEPTED]\" }