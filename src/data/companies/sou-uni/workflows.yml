workflows:
  - name: "sou-uni-negociador-divida-v1"
    description: "Negociador de Dívidas Uni Internet - Fluxo Completo"
    steps:
      - description: "Abertura e apresentação da dívida (sem confirmação)"
        order: 1
        task: sou-uni-abertura-identificacao
        params:
        middlewares:
          - task: sou-uni-obter-status-negociacao
            name: "negotiation-status"
            description: "Extrair o status da negociação da conversa fornecida em formato JSON e o output deve ser ON_GOING ou ACCEPTED"
            type: "POST_EXECUTION"
            showOff: false
          - name: "resumo-negociacao"
            task: sou-uni-gerar-resumo-negociacao
            description: "Middleware que extrai resumo da negociação"
            type: "POST_EXECUTION"
            showOff: true

      - description: "Buscar previsão de pagamento"
        order: 2
        task: sou-uni-buscar-previsao-pagamento
        params:
        middlewares:
          - task: sou-uni-validar-previsao-informada
            name: "forecast-validation"
            description: "Verificar se o cliente informou previsão de pagamento"
            type: "POST_EXECUTION"
            showOff: false
