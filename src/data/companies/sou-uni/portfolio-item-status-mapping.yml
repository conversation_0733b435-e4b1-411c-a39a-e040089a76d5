portfolio-item-status-mapping:
  - workflow: sou-uni-negociador-divida-v1
    postExecutionResponse: "NO_RESPONSE"
    portfolioItemStatus: "IN_PROGRESS"
    responseKey: "middlewaresResponse.client-response-status.value"
  - workflow: sou-uni-negociador-divida-v1
    postExecutionResponse: "RESPONDED"
    portfolioItemStatus: "IN_PROGRESS"
    responseKey: "middlewaresResponse.client-response-status.value"
  - workflow: sou-uni-negociador-divida-v1
    postExecutionResponse: "ON_GOING"
    portfolioItemStatus: "IN_PROGRESS"
    responseKey: "middlewaresResponse.negotiation-status.value"
  - workflow: sou-uni-negociador-divida-v1
    postExecutionResponse: "ACCEPTED"
    portfolioItemStatus: "SUCCEED"
    responseKey: "middlewaresResponse.negotiation-status.value"
  - workflow: sou-uni-negociador-divida-v1
    postExecutionResponse: "INFORMED_FORECAST"
    portfolioItemStatus: "IN_PROGRESS"
    responseKey: "middlewaresResponse.forecast-validation.value"
  - workflow: sou-uni-negociador-divida-v1
    postExecutionResponse: "NO_FORECAST"
    portfolioItemStatus: "IN_PROGRESS"
    responseKey: "middlewaresResponse.forecast-validation.value"
