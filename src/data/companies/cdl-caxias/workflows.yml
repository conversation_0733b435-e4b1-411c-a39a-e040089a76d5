workflows:
  - name: "cdl-caxias-cobrança-dívida-v1"
    description: "Cobrança de dívida"
    steps:
      - description: "Abertura e Identificação"
        order: 1
        task: cdl-caxias-abertura-identificacao
        params:
        middlewares:
          - task: cdl-caxias-validar-contato-pessoa-certa
            name: "routing-middleware"
            description: "Verificar se o usuário já passou pelo processo de verificação de identidade com sucesso"
            type: "POST_EXECUTION"
            showOff: false

      - description: "Negociar dívida"
        order: 2
        task: cdl-caxias-negociador-divida
        params:
        middlewares:
          - task: cdl-caxias-analista-status-negociacao
            name: "interaction-status"
            description: "Extrair o status da negociação da conversa fornecida em formato JSON e o output deve ser ON_GOING ou ACCEPTED"
            type: "POST_EXECUTION"
            showOff: false

  - name: "cdl-caxias-grazziotin-v2"
    description: "Cobrança de dívida"
    steps:
      - description: "Abertura e Identificação"
        order: 1
        task: cdl-caxias-abertura-identificacao
        params:
        middlewares:
          - task: cdl-caxias-validar-contato-pessoa-certa-v2
            name: "interaction-status"
            description: "Verificar se o usuário já passou pelo processo de verificação de identidade com sucesso"
            type: "POST_EXECUTION"
            showOff: false
