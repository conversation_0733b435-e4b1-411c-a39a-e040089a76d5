tasks:
  - name: saf-negotiation-task
    agent: saf-debt-negotiator
    description: |
      Conduza a conversa seguindo o roteiro de mensagens definido.

  - name: saf-interaction-status-agent
    agent: saf-negotiation-analysis
    description: |
      Com base no histórico de negociação fornecido em formato JSON, onde o Usuário é o devedor, analise a conversa para extrair o status da negociação. 
      O status da negociação pode ser: \"ON_GOING\" ou \"ACCEPTED\". 
      - "ON_GOING": Significa que a negociação ainda está em andamento. 
      - "ACCEPTED": Significa que o devedor aceitou a proposta de pagamento e o boleto está sendo gerado ou que o devedor falou que iria presenciamente num unidade do SAF resolver a situação, ou seja pagar em dinheiro ou cartão de crédito. Também significa que foi enviada a mensagem "Aguarde enquanto eu gero o seu boleto." ou algo similar, nesse caso o resultado deve ser "ACCEPTED" também.
      
      A resposta deve ser **somente um JSON válido** que contenha o status da negociação. Retorne apenas o JSON no formato: {\"value\": \"ON_GOING\"} ou {\"value\": \"ACCEPTED\"}. **Não** inclua textos adicionais por exemplo \"```json\".
      
      {{conversationHistory}} 
    responseTemplate: |
      {\"value\": \"[ON_GOING ou ACCEPTED]\" }