workflows:
  - name: cia-de-talentos-workflow-cs-2025
    description: "Carreira dos Sonhos 2025"
    steps:
      - description: "Aqui faremos a saudação e selecionaremos o tipo de participante"
        order: 1
        task: cia-de-talentos-cs-2025-abertura
        params:
        middlewares:
          - task: cia-de-talentos-cs-2025-identificar-Q1-opcao-selecionada
            name: "routing-middleware"
            description: "Com base na resposta da questão Q1, direcione o participante para o formulário correto"
            type: "POST_EXECUTION"
            showOff: false
      - description: "Forms1 - estudante universitário ou ensino técnico sem experiência profissional "
        order: 2
        task: cia-de-talentos-cs-2025-estudante-ou-técnico-sem-experiência
        showOff: false
      - description: "Forms2: estudante universitário ou ensino técnico *com* experiência profissional e Pessoa colaboradora (assistente, analista, consultor, autônomo etc.)"
        order: 3
        task: cia-de-talentos-cs-2025-estudante-ou-técnico-ou-colaborador-com-experiência
        showOff: false
      - description: "Forms2: trainee"
        order: 4
        task: cia-de-talentos-cs-2025-trainee
        showOff: false
      - description: "Média Gestão e Alta Liderança"
        order: 5
        task: cia-de-talentos-cs-2025-média-gestão-e-alta-liderança
        showOff: false
