workflows:
  - name: nef-negociador-divida
    description: "Workflow de negociação de dívidas do Nabarrete & Ferro"
    steps:
      - description: "Negotiate debt"
        order: 1
        task: nef-negotiation-task
        middlewares:
          - name: "interaction-status"
            task: nef-interaction-status-task
            description: "Middleware que extrai o status da interação"
            type: "POST_EXECUTION"
            showOff: false
          - name: "resumo-negociacao"
            task: nef-negotiation-synthesis-task
            description: "Middleware que extrai resumo do que o devedor falou"
            type: "POST_EXECUTION"
            showOff: true

  - name: nef-negociador-divida-follow-up-v1
    description: "Follow up da dívida, datas futuras, tempo de resposta e lembrete de pagamento"
    steps:
      - description: "Follow up da dívida"
        order: 1
        task: nef-negociador-divida-v4-follow-up
        middlewares:
          - task: nef-negociador-dividas-follow-up-status
            name: mustFollow
            description: Verificar se deve ser feito o follow up com base no histórico da
              conversa fornecida em formato JSON e o output deve ser DONT_FOLLOW_UP ou FOLLOW_UP
            type: POST_EXECUTION
            showOff: true
