tasks:
  - name: nef-negotiation-task
    agent: nef-debt-negotiator
    description: |
      Conduza a conversa seguindo o roteiro de mensagens definido.

  - name: nef-interaction-status-task
    agent: nef-negotiation-analysis
    description: |
      Com base no histórico de negociação fornecido em formato JSON, onde o Usuário é o devedor, analise a conversa para extrair o status da negociação. 
      O status da negociação pode ser: \"ON_GOING\" ou \"ACCEPTED\". 
      - "ON_GOING": Significa que a negociação ainda está em andamento. 
      - "ACCEPTED": Significa que o devedor aceitou pagar a dívida no valor total via boleto, ou realizou uma promessa para pagamento nos próximos 10 dias, ou então fez uma proposta para pagamento parcelado, informando valor de entrada, data de vencimento da entrada e dia de vencimento das demais parcelas. 
      
      A resposta deve ser **somente um JSON válido** que contenha o status da negociação. Retorne apenas o JSON no formato: {\"value\": \"ON_GOING\"} ou {\"value\": \"ACCEPTED\"}. **Não** inclua textos adicionais por exemplo \"```json\".
      
      {{conversationHistory}} "
    responseTemplate: |
      {\"value\": \"[ON_GOING ou ACCEPTED]\" }

  - name: nef-negotiation-synthesis-task
    agent: nef-negotiation-synthesis-agent
    description: |
      Com base no histórico de negociação fornecido em formato JSON, onde o Usuário é o devedor, analise a conversa para extrair o resumo da negociação. 
      O resumo deve conter as principais informações sobre o que o devedor falou, incluindo os pontos discutidos e as propostas feitas.
      - Se o devedor não respondeu ou não falou nada: "Devedor não respondeu".
      - Se o devedor não validou o CPF/CNPJ: "Devedor não validou o CPF/CNPJ".
      - Se o devedor não confiar no contato realizado, questionando sobre a segurança do contato: "Devedor não confia no contato realizado".
      - Se o devedor realizar uma promessa de pagamento nos próximos 10 dias: "Devedor prometeu pagar em até 10 dias".
      - Se o devedor aceitar o pagamento do valor total em até 10 dias: "Devedor aceitou pagar o valor total em até 10 dias".
      - Se o devedor realizar um proposta de pagamento onde haverá uma entrada + parcelas: "Devedor propôs pagar com entrada de R$ [valor] com [vencimento da entrada] e [número] parcelas de R$ [valor] com vencimento em todo dia [dia de vencimento]".
      - Se o devedor iformar que não pode pagar: "Devedor informou que não pode pagar".
      - Para demais casos inclua o que o devedor falou de forma resumida.

      {{conversationHistory}} "

  - name: nef-negociador-divida-v4-follow-up
    description:  |
      **REGRAS GERAIS DE COMUNICAÇÃO**

      - **Não** invente informações.
      - Mantenha mensagens *curtas* de até 100 palavras.
      - **Limite-se a responder apenas sobre a negociação da dívida.**
      - **Nunca forneça informações pessoais ou sensíveis.**
      - **Nunca ofereça condições, descontos ou datas de vencimento diferentes das informadas no contexto.**
      - **Nunca antecipe informações não solicitadas.**
      - Seja cordial, empático(a) e utilize linguagem simples, objetiva e adequada para WhatsApp e para públicos das classes B e C.
      - **Nunca** use palavrões ou palavras ofensivas.
      - **Não adicione comentários ou informações adicionais**, responda penas com a mensagem solicitada.
      - Sempre utilize o nome da empresa correto **NEF - Assessoria**.

      **GERAR MENSAGEM DE FOLLOW-UP**
      Gere uma mensagem resumida e suscinta de follow-up para retomar a negociação com o devedor. Seja cordial e empático e objetivo em sua comunicação, buscando reestabelecer o diálogo e a negociação.      

      1. Caso o devedor não tenha respondido à proposta anterior, reforce a importância de resolver a dívida. Seja cordial e empático em sua comunicação, incentivando o devedor a considerar as opções disponíveis.

      2. Caso a conversa tenha parado na apresentação da dívida, reforce os detalhes da dívida e ofereça suporte para esclarecer qualquer dúvida ou informação adicional necessária para dar continuidade à negociação.

      **Histórico de conversa em formato JSON:**

      {{conversationHistory}}
    agent: nef-debt-negotiator

  - name: nef-negociador-dividas-follow-up-status
    description: |
      Com base no histórico de negociação fornecido em formato JSON, onde
      o Usuário é o lead, analise a conversa para extrair o status da interação. E verificar
      se devemos enviar uma mensagem de follow up ou não.  
  
      Caso o usuário, relate que não deseja mais receber mensagens, o status da interação deve ser **\"FINISHED\"** e a mensagem de follow up não deve ser enviado. Nesse caso a resposta deve ser **somente um JSON válido** que contenha o status da negociação. Retorne apenas o JSON no formato:
      {\"value\": \"DONT_FOLLOW_UP\"}
  
      Caso o usuário tenha parado na validação do CPF, o status da interação deve ser **\"ON_GOING\"** e a mensagem de follow up deve ser enviada. Nesse caso a resposta deve ser **somente um JSON válido** que contenha o status da negociação. Retorne apenas o JSON no formato:  
      {\"value\":\"FOLLOW_UP\"}
  
      Caso o usuário relate não ser a pessoa a quem está sendo mencionadaa dívida, o status da interação deve ser **\"FINISHED\"** e a mensagem de follow up não deve ser enviado. Nesse caso a resposta deve ser **somente um JSON válido** que contenha o status da negociação. Retorne apenas o JSON no formato:  
  
      {\"value\":\"DONT_FOLLOW_UP\"}
  
      **Não** inclua textos adicionais.
  
      {{conversationHistory}}
    agent: nef-negociador-divida-v4-follow-up-interaction-status-agent
    responseTemplate: '{"value": "[DONT_FOLLOW_UP ou FOLLOW_UP]" }'