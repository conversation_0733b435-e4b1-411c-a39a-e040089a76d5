workflows:
  - name: "leve-saude-negociador-divida-v1"
    description: "Negociador de Dívidas Leve Saúde"
    steps:
      - description: "Negociar dívida"
        order: 1
        task: leve-saude-negociar-divida
        params:
        middlewares:
          - name: "interaction-status"
            task: leve-saude-obter-status-negociacao
            description: "Extrair o status da negociação da conversa fornecida em formato JSON e o output deve ser ON_GOING ou ACCEPTED"
            type: "POST_EXECUTION"
            showOff: false
          - name: "resumo-negociacao"
            task: leve-saude-gerar-resumo-negociacao
            description: "Middleware que extrai resumo da negociação"
            type: "POST_EXECUTION"
            showOff: true

  - name: "leve-saude-negociador-divida-pf-v1"
    description: "Negociador de Dívidas Leve Saúde - Pessoa Física"
    steps:
      - description: "Negociar dívida"
        order: 1
        task: leve-saude-negociar-divida-pf
        params:
        middlewares:
          - name: "interaction-status"
            task: leve-saude-obter-status-negociacao
            description: "Extrair o status da negociação da conversa fornecida em formato JSON e o output deve ser ON_GOING ou ACCEPTED"
            type: "POST_EXECUTION"
            showOff: false
          - name: "resumo-negociacao"
            task: leve-saude-gerar-resumo-negociacao
            description: "Middleware que extrai resumo da negociação"
            type: "POST_EXECUTION"
            showOff: true
