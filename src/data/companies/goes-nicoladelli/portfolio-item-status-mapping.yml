portfolio-item-status-mapping:
  - workflow: goes-nicoladelli-negociador-divida
    postExecutionResponse: "IN_PROGRESS"
    portfolioItemStatus: "IN_PROGRESS"
    responseKey: "middlewaresResponse.iteration-status.value"
  - workflow: goes-nicoladelli-negociador-divida
    postExecutionResponse: "SUCCEED"
    portfolioItemStatus: "SUCCEED"
    responseKey: "middlewaresResponse.iteration-status.value"
  - workflow: goes-nicoladelli-negociador-divida
    postExecutionResponse: "OPTED_OUT"
    portfolioItemStatus: "OPTED_OUT"
    responseKey: "middlewaresResponse.iteration-status.value"
