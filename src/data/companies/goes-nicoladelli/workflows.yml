workflows:
  - name: "goes-nicoladelli-negociador-divida"
    description: "Negociar dívida"
    steps:
      - description: "Negociar dívida"
        order: 1
        task: goes-nicoladelli-negociar-divida
        params:
        middlewares:
          - task: goes-nicoladelli-obter-status-negociacao
            name: "iteration-status"
            description: "Extrair o status da negociação da conversa fornecida em formato JSON e o output deve ser IN_PROGRESS ou SUCCEED"
            type: "POST_EXECUTION"
            showOff: false
          - name: "resumo-negociacao"
            task: goes-nicoladelli-gerar-resumo-negociacao
            description: "Middleware que extrai resumo da negociação"
            type: "POST_EXECUTION"
            showOff: true
