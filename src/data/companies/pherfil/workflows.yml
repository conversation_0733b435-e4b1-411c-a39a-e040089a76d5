workflows:
  - name: "pherfil-negociador-credsystem-v3"
    description: "Negociador de Dívidas Credsystem v3"
    steps:
      - description: "Abertura e Identificação"
        order: 1
        task: pherfil-abertura-identificacao
        params:
        middlewares:
          - task: pherfil-credsystem-validar-contato-pessoa-certa
            name: "routing-middleware"
            description: "Verificar se o usuário já passou pelo processo de verificação de identidade com sucesso"
            type: "POST_EXECUTION"
            showOff: false

      - description: "Negociar dívida"
        order: 2
        task: pherfil-credsystem-negociador-divida-v3
        params:
        middlewares:
          - task:
            name: "pherfil-nectar-integration"
            description: "Obter as opções de acordo no sistema Nectar"
            category: "INTEGRATION"
            type: "POST_START"
            params:
              - integration_name: "nectar-integration"
            showOff: false
          - task: pherfil-credsystem-negociador-dividas-iteration-status
            name: "iteration-status"
            description: "Extrair o status da negociação da conversa fornecida em formato JSON e o output deve ser ON_GOING ou ACCEPTED"
            type: "POST_EXECUTION"
            showOff: false
          - task: pherfil-credsystem-negociador-dividas-acordo-info
            name: "acordo-info"
            description: "Extrair os dados sobre o acordo fechado"
            type: "POST_EXECUTION"
            showOff: false

  - name: "pherfil-negociador-credsystem-v4"
    description: "Negociador de Dívidas Credsystem v4"
    steps:
      - description: "Abertura e Identificação"
        order: 1
        task: pherfil-abertura-identificacao
        params:
        middlewares:
          - task: pherfil-credsystem-validar-contato-pessoa-certa
            name: "routing-middleware"
            description: "Verificar se o usuário já passou pelo processo de verificação de identidade com sucesso"
            type: "POST_EXECUTION"
            showOff: false
          - task: pherfil-credsystem-negociador-dividas-iteration-status
            name: "iteration-status"
            description: "Extrair o status da negociação da conversa fornecida em formato JSON e o output deve ser ON_GOING ou ACCEPTED"
            type: "POST_EXECUTION"
            showOff: false
          - task: pherfil-credsystem-negociador-divida-scheduled-follow-up-status
            name: "follow-up-scheduler"
            description: "Extrair o status da negociação da conversa fornecida em formato JSON e extrair as datas de follow-up"
            type: "POST_EXECUTION"
            showOff: true

      - description: "Negociar dívida"
        order: 2
        task: pherfil-credsystem-negociador-divida-v3
        params:
        middlewares:
          - task:
            name: "pherfil-nectar-integration"
            description: "Obter as opções de acordo no sistema Nectar"
            category: "INTEGRATION"
            type: "POST_START"
            params:
              - integration_name: "nectar-integration"
            showOff: false
          - task: pherfil-credsystem-negociador-dividas-iteration-status
            name: "iteration-status"
            description: "Extrair o status da negociação da conversa fornecida em formato JSON e o output deve ser ON_GOING ou ACCEPTED"
            type: "POST_EXECUTION"
            showOff: false
          - task: pherfil-credsystem-negociador-dividas-acordo-info
            name: "acordo-info"
            description: "Extrair os dados sobre o acordo fechado"
            type: "POST_EXECUTION"
            showOff: false
          - task: pherfil-credsystem-negociador-divida-scheduled-follow-up-status
            name: "follow-up-scheduler"
            description: "Extrair o status da negociação da conversa fornecida em formato JSON e extrair as datas de follow-up"
            type: "POST_EXECUTION"
            showOff: true
          - task: pherfil-negociador-dividas-deal-info-v1
            name: "deal-info"
            description: "Extrair dados do acordo fechado, como valor total, valor da entrada, número de parcelas, data de vencimento e data de início do pagamento"
            type: "POST_EXECUTION"
            showOff: true

  - name: "pherfil-negociador-credsystem-follow-up-v1"
    description: "Follow up da dívida, datas futuras, tempo de resposta e lembrete de pagamento"
    steps:
      - description: "Follow up da dívida"
        order: 1
        task: pherfil-credsystem-gerar-mensagem-follow-up
        middlewares:
          - task: pherfil-credsystem-negociador-dividas-follow-up-status
            name: mustFollow
            description:
              Verificar se deve ser feito o follow up com base no histórico da
              conversa fornecida em formato JSON e o output deve ser DONT_FOLLOW_UP ou FOLLOW_UP
            type: POST_EXECUTION
            showOff: true
