portfolio-item-status-mapping:
  - workflow: versacredi-workflow-qualification
    postExecutionResponse: "ON_GOING"
    portfolioItemStatus: "IN_PROGRESS"
    responseKey: "middlewaresResponse.interaction-status.value"
  #tests
  - workflow: versacredi-workflow-followup
    postExecutionResponse: "ACCEPTED"
    portfolioItemStatus: "SUCCEED"
    responseKey: "middlewaresResponse.iteration-status.value"
