tasks:
  - name: versacredi-qualify-lead #versacredi-lead-qualifier / deixar como prefixo
    agent: versacredi-lead-qualifier
    description: |
      Conduza a conversa seguindo o roteiro de perguntas definido.

  - name: versacredi-calculate-score-lead
    agent: versacredi-lead-qualifier-score #outro agent que calcula o score
    description: |
      Você é um assistente responsável por calcular score do cliente com base no histórico da conversa.
      A avaliacao é quantitativa, ou seja, quanto mais pontos, melhor.
      ---

      ### **Instruções para o Cálculo**

      - Atribua os pontos conforme as regras fornecidas abaixo.
      - Some todos os pontos para obter o score total.
      - Com base no score total, escreva uma justificativa curta.

      #### **1. Interesse e Intenção de Compra**
      - **Participação anterior em consórcio:**
        - "Sim, e foi uma boa experiência" → +5 pontos
        - "Sim, mas não gostei" → +2 pontos
        - "<PERSON><PERSON>, mas conheço o funcionamento" → +5 pontos
        - "N<PERSON>, e não sei muito sobre o assunto" → +0 pontos

      - **Urgência na aquisição do bem:**
        - "Sim, preciso o quanto antes" → +10 pontos
        - "Quero comprar, mas sem pressa" → +5 pontos
        - "Só estou pesquisando por enquanto" → +0 pontos

      - **Motivo principal para considerar o consórcio:**
        - "Investimento" → +10 pontos
        - "Planejamento financeiro" → +8 pontos
        - "Substituir um financiamento" → +5 pontos
        - "Compra programada de um bem específico" → +10 pontos

      ---

      #### **2. Capacidade Financeira**
      - **Valor do bem pretendido:**
        - "Até R$100 mil" → +5 pontos
        - "De R$100 mil a R$250 mil" → +10 pontos
        - "Acima de R$250 mil" → +15 pontos

      - **Valor reservado para lance:**
        - "Mais de 30% do valor do bem" → +15 pontos
        - "Entre 10% e 30%" → +10 pontos
        - "Menos de 10%" → +5 pontos
        - "Nenhum, pretendo ser contemplado pelo sorteio" → +0 pontos

      - **Faixa de parcela mensal confortável:**
        - "Acima de R$3.000" → +15 pontos
        - "R$1.500 - R$3.000" → +10 pontos
        - "R$500 - R$1.500" → +5 pontos
        - "Menos de R$500" → +0 pontos

      - **Compromissos financeiros existentes:**
        - "Não, estou tranquilo financeiramente" → +10 pontos
        - "Sim, mas posso assumir outro compromisso" → +5 pontos
        - "Sim, e não posso comprometer mais minha renda" → +0 pontos

      ---

      #### **3. Necessidade e Perfil do Cliente**
      - **Finalidade do bem:**
        - "Investimento" → +10 pontos
        - "Uso próprio" → +8 pontos
        - "Apenas avaliando opções" → +2 pontos

      - **Tempo para utilizar o crédito:**
        - "Assim que for contemplado" → +10 pontos
        - "Nos próximos 6 meses" → +8 pontos
        - "Em mais de 1 ano" → +2 pontos

      - **Pesquisa sobre consórcio:**
        - "Sim, e estou comparando" → +10 pontos
        - "Sim, mas ainda não decidi" → +5 pontos
        - "Não, estou começando a pesquisar agora" → +2 pontos

      ---

      #### **4. Objeções e Expectativas**
      - **Principais dúvidas sobre o consórcio:**
        - "Nenhuma, já entendi como funciona" → +10 pontos
        - "Quero entender mais sobre contemplação e lances" → +5 pontos
        - "Tenho receio sobre a segurança e confiabilidade" → +2 pontos

      - **Possui restrições no nome:**
        - "Não" → +5 pontos
        - "Sim, mas posso resolver em breve" → +2 pontos
        - "Sim, e não consigo resolver agora" → +0 pontos
      ---

      ### Formato de resposta

        - A saída deve ser **exatamente** como o exemplo abaixo, com as informações incluindo as informações que foram calculadas:
        "*Resumo da Análise*
        *Pontuação total:* [Score calculado].
        *Justificativa:* [Justificativa curta para o score calculado].

      Segue o historico da conversa abaixo para o calculo de score:

      {{conversationHistory}}

  - name: versacredi-interaction-status
    agent: versacredi-interaction-status
    description: "Com base no histórico de negociação fornecido em formato JSON, onde o Usuário é o lead, analise a conversa para extrair o status da interação. O status da interação pode ser: \"ON_GOING\" ou \"FINISHED\". \n- \"ON_GOING\": Significa que o lead ainda está sendo conduzido pelo assistente.\n- \"FINISHED\": Significa que o assistente finalizou a interação com o lead. \n\nA resposta deve ser **somente um JSON válido** que contenha o status da negociação. Retorne apenas o JSON no formato: {\"value\": \"ON_GOING\"} ou {\"value\": \"FINISHED\"}. **Não** inclua textos adicionais por exemplo \"```json\".\n\n{{conversationHistory}}"

  # tests
  - name: versacredi-followup-lead
    agent: versacredi-followup-agent
    description: |
      Conduza o acompanhamento do lead conforme o roteiro definido.

  - name: versacredi-calculate-followup-score
    agent: versacredi-followup-agent
    description: |
      Calcula o score de acompanhamento do lead com base nas interações.

  - name: versacredi-interaction-followup-status
    agent: versacredi-followup-agent
    description: |
      Extrai o status de acompanhamento da interação com o lead.
