portfolios:
  - name: versa-credi-portfolio
    workflow: versacredi-workflow-qualification
    workExpression: "*/2 * * * *"
    followUpExpression: "* * * * * *"
    followUpAfter: 24
    followUpWorkflowId:
    maxFollowUps: 5
    totalQuantity: 0
    processedQuantity: 0
    totalSuccessQuantity: 0
    totalFailedQuantity: 0
    executionStatus: "WAITING"
    importStatus: "UPLOADED"
    processingRateLimit: 100
    status: "ACTIVE"
    communicationChannel: "WHATSAPPSELFHOSTED"
    idleAfter: 10
    isDefault: false

  - name: versa-credi-portfas
    workflow: versacredi-workflow-followup
    workExpression: "*/5 * * * *"
    followUpExpression: "* * * * * *"
    followUpAfter: 24
    followUpWorkflowId:
    maxFollowUps: 5
    totalQuantity: 0
    processedQuantity: 0
    totalSuccessQuantity: 0
    totalFailedQuantity: 0
    executionStatus: "WAITING"
    importStatus: "UPLOADED"
    processingRateLimit: 100
    status: "ACTIVE"
    communicationChannel: "WHATSAPPSELFHOSTED"
    idleAfter: 10
    isDefault: false
