workflows:
  - name: versacredi-workflow-qualification
    description: "Workflow de qualificação de leads da VersaCredi"
    steps:
      - description: "Qualificar lead"
        order: 1
        task: versacredi-qualify-lead
        middlewares:
          - name: "calculate-score"
            task: versacredi-calculate-score-lead
            description: "Middleware que calcula o score do lead"
            type: "POST_EXECUTION"
            showOff: true
          - name: "interaction-status"
            task: versacredi-interaction-status
            description: "Middleware que extrai o status da interação"
            type: "POST_EXECUTION"
            showOff: false
  # tests
  - name: versacredi-workflow-followup
    description: "Workflow de acompanhamento de leads da VersaCredi"
    steps:
      - description: "Acompanhar lead"
        order: 2
        task: versacredi-followup-lead
        middlewares:
          - name: "calculate-followup-score"
            task: versacredi-calculate-followup-score
            description: "Middleware que calcula o score de acompanhamento do lead"
            type: "POST_EXECUTION"
            showOff: true
          - name: "interaction-followup-status"
            task: versacredi-interaction-followup-status
            description: "Middleware que extrai o status de acompanhamento da interação"
            type: "POST_EXECUTION"
            showOff: false
