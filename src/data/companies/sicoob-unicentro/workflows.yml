workflows:
  - name: "sicoob-unicentro-negociador-divida-v1"
    description: "Negociador de Dívidas Sicoob Unicentro - Fluxo Completo"
    steps:
      - description: "Abertura e apresentação da dívida (sem confirmação)"
        order: 1
        task: sicoob-unicentro-abertura-identificacao
        params:
        middlewares:
          - task: sicoob-unicentro-obter-status-negociacao
            name: "negotiation-status"
            description: "Extrair o status da negociação da conversa fornecida em formato JSON e o output deve ser ON_GOING ou ACCEPTED"
            type: "POST_EXECUTION"
            showOff: false
          - name: "resumo-negociacao"
            task: sicoob-unicentro-gerar-resumo-negociacao
            description: "Middleware que extrai resumo da negociação"
            type: "POST_EXECUTION"
            showOff: true

      - description: "Buscar previsão de pagamento"
        order: 2
        task: sicoob-unicentro-buscar-previsao-pagamento
        params:
        middlewares:
          - task: sicoob-unicentro-validar-previsao-informada
            name: "forecast-validation"
            description: "Verificar se o cliente informou previsão de pagamento"
            type: "POST_EXECUTION"
            showOff: false
