import { UpdateCustomerPreferencesPort } from "../../infrastructure/ports/http/update-customer-preferences.port";

import { UpdateCustomerPreferencesResponseDto } from "../../infrastructure/dtos/out/update-customer-preferences-response.dto";
import { CreateCustomerPreferencesRequestDto } from "../../infrastructure/dtos/in/upsert-customer-preferences-request.dto";

export class UpdateCustomerPreferencesUseCase {
  constructor(private readonly updateCustomerPreferencesPort: UpdateCustomerPreferencesPort) { }

  async execute(
    customerId: string,
    data: CreateCustomerPreferencesRequestDto,
    companyName: string
  ): Promise<UpdateCustomerPreferencesResponseDto> {
    return await this.updateCustomerPreferencesPort.update(customerId, data, companyName);
  }
}
