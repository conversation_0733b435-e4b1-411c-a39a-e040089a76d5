import { CreateCustomerPreferencesPort } from "../../infrastructure/ports/http/create-customer-preferences.port";
import { CreateCustomerPreferencesRequestDto } from "../../infrastructure/dtos/in/upsert-customer-preferences-request.dto";
import { CreateCustomerPreferencesResponseDto } from "../../infrastructure/dtos/out/create-customer-preferences-response.dto";

export class CreateCustomerPreferencesUseCase {
  constructor(private readonly createCustomerPreferencesPort: CreateCustomerPreferencesPort) { }

  async execute(
    data: CreateCustomerPreferencesRequestDto,
    companyName: string
  ): Promise<CreateCustomerPreferencesResponseDto> {
    return await this.createCustomerPreferencesPort.create(data, companyName);
  }
}
