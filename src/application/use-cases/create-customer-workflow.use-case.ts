import { CreateWorkflowAdapter } from "../../infrastructure/adapters/create-customer-workflow.adapter";
import { CreateWorkflowRequestDto } from "../../infrastructure/dtos/in/create-customer-workflow-request.dto";
import { CreateWorkflowResponseDto } from "../../infrastructure/dtos/out/create-customer-workflow-response.dto";
import { GetCustomerWorkflowsResponseDto } from "../../infrastructure/dtos/out/get-customer-workflows-response.dto";

export class CreateWorkflowUseCase {
  constructor(private readonly adapter: CreateWorkflowAdapter) {}

  async execute(
    data: CreateWorkflowRequestDto,
    companyName: string
  ): Promise<CreateWorkflowResponseDto> {
    return await this.adapter.create(data, companyName);
  }

  async associateWorkflow(
    customerId: string,
    workflowId: string,
    companyName: string
  ): Promise<GetCustomerWorkflowsResponseDto> {
    return await this.adapter.associateWorkflow(
      customerId,
      workflowId,
      companyName
    );
  }
}
