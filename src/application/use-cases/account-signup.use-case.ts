import { AccountSignupPort } from "../../infrastructure/ports/http/account-signup.port";
import { CompanySignupRequestDto } from "../../infrastructure/dtos/in/company-signup-request.dto";
import { CompanySignupResponseDto } from "../../infrastructure/dtos/out/company-signup-response.dto";

export class AccountSignupUseCase {
  constructor(private readonly accountSignupPort: AccountSignupPort) {}

  async execute(
    data: CompanySignupRequestDto
  ): Promise<CompanySignupResponseDto> {
    return await this.accountSignupPort.signup(data);
  }
}
