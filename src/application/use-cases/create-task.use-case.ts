import { CreateTaskPort } from "../../infrastructure/ports/http/create-task.port";
import { CreateTaskRequestDto } from "../../infrastructure/dtos/in/create-task-request.dto";
import { CreateTaskResponseDto } from "../../infrastructure/dtos/out/create-task-response.dto";

export class CreateTaskUseCase {
  constructor(private readonly createTaskPort: CreateTaskPort) {}

  async execute(
    data: CreateTaskRequestDto,
    companyName: string
  ): Promise<CreateTaskResponseDto> {
    return await this.createTaskPort.create(data, companyName);
  }
}
