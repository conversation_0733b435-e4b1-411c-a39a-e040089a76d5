import { CreateAgentPort } from "../../infrastructure/ports/http/create-agent.port";
import { CreateAgentRequestDto } from "../../infrastructure/dtos/in/create-agent-request.dto";
import { CreateAgentResponseDto } from "../../infrastructure/dtos/out/create-agent-response.dto";

export class CreateAgentUseCase {
  constructor(private readonly createAgentPort: CreateAgentPort) {}

  async execute(
    data: CreateAgentRequestDto,
    companyName: string
  ): Promise<CreateAgentResponseDto> {
    return await this.createAgentPort.create(data, companyName);
  }
}
