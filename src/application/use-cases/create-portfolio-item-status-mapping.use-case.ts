import { CreatePortfolioStatusMappingPort } from "../../infrastructure/ports/http/create-portfolio-item-status-mapping.port";
import { CreatePortfolioStatusMappingRequestDto } from "../../infrastructure/dtos/in/create-portfolio-item-status-mapping-request.dto";
import {
  CreatePortfolioStatusMappingResponseDto,
  AssociateWorkflowResponseDto,
} from "../../infrastructure/dtos/out/create-portfolio-item-status-mapping-response.dto";

export class CreatePortfolioStatusMappingUseCase {
  constructor(
    private readonly createPortfolioStatusMappingPort: CreatePortfolioStatusMappingPort
  ) {}

  public async execute(
    data: CreatePortfolioStatusMappingRequestDto,
    companyName: string
  ): Promise<CreatePortfolioStatusMappingResponseDto> {
    return await this.createPortfolioStatusMappingPort.create(
      data,
      companyName
    );
  }

  public async associateWorkflow(
    workflowId: string,
    customerId: string,
    companyName: string
  ): Promise<AssociateWorkflowResponseDto> {
    return await this.createPortfolioStatusMappingPort.associateWorkflow(
      workflowId,
      customerId,
      companyName
    );
  }
}
