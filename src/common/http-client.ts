import axios from "axios";
import * as path from "path";
import * as yaml from "js-yaml";
import * as fs from "fs";

interface CompanyData {
  userData: {
    email: string;
    password: string;
  };
}

async function getBearerToken(companyName: string): Promise<string> {
  const COMPANY_PATH = path.resolve(
    __dirname,
    `../data/companies/${companyName}`
  );
  const companyData = yaml.load(
    fs.readFileSync(path.resolve(COMPANY_PATH, "company.yml"), "utf8")
  ) as CompanyData;

  const email = companyData.userData.email;
  const password = companyData.userData.password;

  const loginData = { email, password };
  try {
    const response = await axios.post(
      `${process.env.API_URL}/api/v1/auth/session/login`,
      loginData,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    const cookies = response.headers["set-cookie"];
    if (!cookies) {
      throw new Error("No cookies found in response.");
    }

    const tokenCookie = cookies.find((cookie) =>
      cookie.startsWith("digai.accessToken=")
    );
    if (!tokenCookie) {
      throw new Error("Failed to obtain bearer token from cookie.");
    }

    const token = tokenCookie.split("=")[1].split(";")[0];
    return token;
  } catch (error) {
    console.error("Error obtaining authentication token:", error);
    throw new Error("Failed to obtain bearer token.");
  }
}

export async function fetch(
  companyName: string,
  url: string,
  method: "get" | "post" | "put" | "delete",
  data?: any
): Promise<any> {
  const token = await getBearerToken(companyName);

  try {
    const response = await axios({
      method,
      url,
      data,
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      timeout: 10000,
      httpAgent: new (require("http").Agent)({ keepAlive: false }),
      httpsAgent: new (require("https").Agent)({ keepAlive: false }),
    });

    return response;
  } catch (error) {
    console.error(
      `Error making ${method.toUpperCase()} request to ${url}:`,
      error
    );
    throw error;
  }
}
