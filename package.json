{"name": "transcendence-customer-setup", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "start": "node main.js", "setup": "ts-node src/commands/customer/setup.ts", "agent:edit": "ts-node src/commands/agent/edit-agent.ts", "agent:delete": "ts-node src/commands/agent/delete-agent.ts", "task:edit": "ts-node src/commands/task/edit-task.ts", "task:delete": "ts-node src/commands/task/delete-task.ts", "customer-phones": "ts-node src/commands/customer-phone/create-customer-phone.ts", "customer-phone:delete": "ts-node src/commands/customer-phone/delete-customer-phone.ts", "workflow:add": "ts-node src/commands/workflow/add-workflow.ts", "workflow:edit": "ts-node src/commands/workflow/edit-workflow.ts", "workflow:delete": "ts-node src/commands/workflow/delete-workflow.ts", "portfolio:add": "ts-node src/commands/portfolio/add-portfolio.ts", "portfolio:edit": "ts-node src/commands/portfolio/edit-portfolio.ts", "portfolio:delete": "ts-node src/commands/portfolio/delete-portfolio.ts", "customer-preferences:create": "ts-node src/commands/customer/create-customer-preferences.ts", "customer-preferences:update": "ts-node src/commands/customer/update-customer-preferences.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/js-yaml": "^4.0.9", "@types/node": "^22.13.0", "@types/uuid": "^10.0.0", "js-yaml": "^4.1.0", "ts-node": "^10.9.2", "typescript": "^5.7.3", "uuid": "^11.0.5"}, "dependencies": {"axios": "^1.7.9", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^16.4.7", "reflect-metadata": "^0.2.2"}}