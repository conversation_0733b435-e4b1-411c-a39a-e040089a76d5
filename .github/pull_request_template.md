## Title

- **Short and Clear:** The title should succinctly reflect the primary purpose of the PR.
- **Start with a Verb:** Using verbs like "Add", "<PERSON>move", "<PERSON>factor", or "Fix" can quickly give context about the nature of the change.
- **Follow the pattern:** [IssueType] [Issue Id] Title. Ex. [Task] [EA-123] Refactor getCategories() function
- **No Ending Period:** Titles are like headers, so they shouldn't have punctuation at the end.
- **Limit Length:** Aim for no more than 50 characters (not including IssueType and Issue Id prefixes) so that they display well on most interfaces.

## Description

- **Context:** Start with a brief explanation of why this PR is happening. What's the problem or need to be addressed?
- **Enumeration:** If there are multiple significant changes, enumerate them.
  Link to Tickets or Tasks: Include references or links to Jira Issues.
- **Deployment or Migration Instructions:** If there are special deployment steps or specific migrations, indicate them.

## Type of Change

- [ ] Created agent, task, workflow, etc.
- [ ] Changed agent, task, workflow, etc.
- [ ] New feature (non-breaking change that adds functionality)
- [ ] Breaking change (causes existing functionality to break or behave differently)
- [ ] Deprecated (feature marked for future removal)
- [ ] Removed (feature already removed)
- [ ] Bug fix (non-breaking fix for an issue)
- [ ] Security (fix for vulnerabilities or new security features)
- [ ] Add logs
- [ ] Update repository setup

## Checklist:

- [ ] I have self-reviewed my code
- [ ] I have made corresponding changes to the documentation
- [ ] I have tested the changes (when PR is made in main branch)

More information is available in [Confluence](https://edutalent.atlassian.net/wiki/spaces/EDUTALENT1/pages/32440321/Guidelines+for+contributions+in+our+repositories)
