name: Run Task After PR Approval via Comment

on:
  issue_comment:
    types: [created]

jobs:
  run-task:
    if: github.event.issue.pull_request != null &&
      startsWith(github.event.comment.body, '/run npm run ')
    runs-on: ubuntu-latest

    permissions:
      contents: read
      pull-requests: read

    steps:
      - name: Check if PR is approved
        id: approval_check
        uses: octokit/request-action@v2.x
        with:
          route: GET /repos/${{ github.repository }}/pulls/${{ github.event.issue.number }}/reviews
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Fail if not approved
        run: |
          echo '${{ steps.approval_check.outputs.data }}' > reviews.json
          APPROVED=$(jq '[.[] | select(.state == "APPROVED")] | length' reviews.json)
          echo "✅ Total approved reviews: $APPROVED"
          if [ "$APPROVED" -lt 1 ]; then
            echo "❌ PR ainda não foi aprovado. Abortando execução."
            exit 1
          fi

      - name: Checkout PR code
        uses: actions/checkout@v3
        with:
          ref: refs/pull/${{ github.event.issue.number }}/merge

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install dependencies
        run: npm ci

      - name: Extract and run the command
        env:
          API_URL: ${{ vars.API_URL }}
        run: |
          COMMENT="${{ github.event.comment.body }}"
          COMMAND=$(echo "$COMMENT" | sed -n 's|^/run ||p')
          echo "🟢 Executando: $COMMAND"
          eval "$COMMAND"
