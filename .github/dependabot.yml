version: 2
updates:
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "saturday"
    target-branch: "main"
    groups:
      all-updates:
        patterns:
          - "*"
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "saturday"
    target-branch: "main"
    groups:
      all-updates:
        patterns:
          - "*"
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "saturday"
    target-branch: "main"
    groups:
      all-updates:
        patterns:
          - "*"
