# Company Setup

This project provides a command-line interface (CLI) to create and manage company-related structures such as **Company**, **Workflows**, **Agents**, **Tasks**, **Portfolios**, and **Customer Phones**.

For more information about the setup process, please visit: https://docs.google.com/document/d/1sXEdNG12xu2MYHaiXumRNxHVTgTKvjdZgiN1NorTqXw/edit?usp=sharing

---

## 📦 Prerequisites

- Node.js installed
- Project set up with `npm` scripts
- Terminal access

---

### Company Setup

This command initializes a new company by creating default agents, tasks, workflows, portfolios...

```bash
npm run setup <company-name>
```

**Example:**

```bash
npm run setup digai
```

### Customer Preferences

Manage customer preferences including portfolio settings, timezone configuration, cron expressions, and export preferences.

- **Create Customer Preferences**: Creates new preferences for a customer based on the customer-preferences.yml file.

  ```bash
  npm run customer-preferences:create company=<company-name>
  ```

  **Example:**

  ```bash
  npm run customer-preferences:create company=digai
  ```

- **Update Customer Preferences**: Updates existing customer preferences based on the customer-preferences.yml file.

  ```bash
  npm run customer-preferences:update company=<company-name>
  ```

  **Example:**

  ```bash
  npm run customer-preferences:update company=digai
  ```

### Customer Phones

Manage customer phone numbers associated with a company.

- **Create Customer Phone**: Associates a customer phone number with a specified company.

  ```bash
  npm run customer-phones company=<company-name>
  ```

  **Example:**

  ```bash
  npm run customer-phones company=digai
  ```

- **Delete Customer Phone**: Deletes a customer phone number associated with a specified company.

  ```bash
  npm run customer-phone:delete customer-phone=<phone-number> company-name=<company-name>
  ```

  **Example:**

  ```bash
  npm run customer-phone:delete customer-phone=5511966350308 company-name=digai
  ```

### Agent

Manage agents for a company.

- **Edit Agent**: Edits the details of an existing agent for a specified company.

  ```bash
  npm run agent:edit agent-name=<agent-name> company-name=<company-name>
  ```

  **Example:**

  ```bash
  npm run agent:edit agent-name=digai-demo company-name=digai
  ```

- **Delete Agent**: Removes an agent from a specified company.

  ```bash
  npm run agent:delete agent-name=<agent-name> company-name=<company-name>
  ```

  **Example:**

  ```bash
  npm run agent:delete agent-name=digai-demo company-name=digai
  ```

### Task

Manage tasks for a company.

- **Edit Task**: Modifies the details of an existing task for a specified company.

  ```bash
  npm run task:edit task-name=<task-name> company-name=<company-name>
  ```

  **Example:**

  ```bash
  npm run task:edit task-name=digai-demo-lead company-name=digai
  ```

- **Delete Task**: Deletes a task from a specified company.

  ```bash
  npm run task:delete task-name=<task-name> company-name=<company-name>
  ```

  **Example:**

  ```bash
  npm run task:delete task-name=digai-demo-lead company-name=digai
  ```

### Workflow

Manage workflows for a company.

- **Add Workflow**: Adds a new workflow to an existing company setup.

  ```bash
  npm run workflow:add -- --name=<workflow-name> --company-name=<company-name>
  ```

  **Example:**

  ```bash
  npm run workflow:add -- --name=digai-workflow-followup --company-name=digai
  ```

- **Edit Workflow**: Updates the configuration of an existing workflow for a specified company.

  ```bash
  npm run workflow:edit workflow-name=<workflow-name> company-name=<company-name>
  ```

  **Example:**

  ```bash
  npm run workflow:edit workflow-name=digai-workflow-followup company-name=digai
  ```

- **Delete Workflow**: Removes a workflow from a specified company.

  ```bash
  npm run workflow:delete workflow-name=<workflow-name> company-name=<company-name>
  ```

  **Example:**

  ```bash
  npm run workflow:delete workflow-name=digai-workflow-followup company-name=digai
  ```

### Portfolio

Manage portfolios for a company.

- **Add Portfolio**: Creates a new portfolio for a specified company, associating it with a workflow.

  ```bash
  npm run portfolio:add -- --name=<portfolio-name> --company-name=<company-name>
  ```

  **Example:**

  ```bash
  npm run portfolio:add -- --name=digai-portfolio --company-name=digai
  ```

- **Edit Portfolio**: Updates the configuration of an existing portfolio for a specified company.

  ```bash
  npm run portfolio:edit portfolio-name=<portfolio-name> company-name=<company-name>
  ```

  **Example:**

  ```bash
  npm run portfolio:edit portfolio-name=digai-portfolio company-name=digai
  ```

- **Delete Portfolio**: Removes a portfolio from a specified company by fetching the portfolio name from a YAML file and using it to find the portfolio ID for deletion.

  ```bash
  npm run portfolio:delete portfolio-name=<portfolio-name> company-name=<company-name>
  ```

  **Example:**

  ```bash
  npm run portfolio:delete portfolio-name=digai-portfolio company-name=digai
  ```

---

## 💡 Tips

- Use consistent naming for easier management.
- These commands automate repetitive tasks to save time and reduce human error.
- After create the customer and customer phones don't forget to create the queue to recently created customer.
  - For localhost: aws --endpoint-url=http://localhost:4566 sqs create-queue --queue-name portfolio-item-[customer-id]sufix

---

## 🛠 Maintenance

If you need to modify these commands, check the `scripts` section in `package.json` and related files in the scripts folder.
